import React from 'react';
import { useTheme } from 'next-themes';
import { <PERSON>, Moon, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  variant?: 'default' | 'compact' | 'icon-only';
  showLabel?: boolean;
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  variant = 'default', 
  showLabel = true,
  className 
}) => {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const { t, isRTL } = useLanguage();

  const themes = [
    {
      value: 'light',
      label: isRTL ? 'فاتح' : 'Light',
      icon: Sun,
      description: isRTL ? 'الوضع الفاتح' : 'Light mode'
    },
    {
      value: 'dark',
      label: isRTL ? 'داكن' : 'Dark',
      icon: Moon,
      description: isRTL ? 'الوضع الداكن' : 'Dark mode'
    },
    {
      value: 'system',
      label: isRTL ? 'تلقائي' : 'System',
      icon: Monitor,
      description: isRTL ? 'حسب إعدادات النظام' : 'Follow system preference'
    }
  ];

  const currentTheme = themes.find(t => t.value === theme) || themes[2];
  const CurrentIcon = currentTheme.icon;

  if (variant === 'icon-only') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "relative h-9 w-9 rounded-lg transition-all duration-200",
              "hover:bg-accent hover:text-accent-foreground",
              "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
              className
            )}
            aria-label={t('settings.theme')}
          >
            <CurrentIcon className="h-4 w-4 transition-all duration-200" />
            <span className="sr-only">{currentTheme.description}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align={isRTL ? "start" : "end"} 
          className="w-48 bg-popover border border-border shadow-lg"
        >
          {themes.map((themeOption) => {
            const Icon = themeOption.icon;
            return (
              <DropdownMenuItem
                key={themeOption.value}
                onClick={() => setTheme(themeOption.value)}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 cursor-pointer transition-colors",
                  "hover:bg-accent hover:text-accent-foreground",
                  "focus:bg-accent focus:text-accent-foreground",
                  theme === themeOption.value && "bg-accent/50 text-accent-foreground",
                  isRTL && "flex-row-reverse"
                )}
              >
                <Icon className="h-4 w-4" />
                <div className={cn("flex flex-col", isRTL && "text-right")}>
                  <span className="text-sm font-medium">{themeOption.label}</span>
                  <span className="text-xs text-muted-foreground">
                    {themeOption.description}
                  </span>
                </div>
                {theme === themeOption.value && (
                  <div className="ml-auto h-2 w-2 rounded-full bg-primary" />
                )}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  if (variant === 'compact') {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => {
          const nextTheme = theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light';
          setTheme(nextTheme);
        }}
        className={cn(
          "flex items-center gap-2 h-8 px-3 rounded-lg transition-all duration-200",
          "hover:bg-accent hover:text-accent-foreground",
          "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
          className
        )}
        aria-label={`${t('settings.theme')}: ${currentTheme.label}`}
      >
        <CurrentIcon className="h-3.5 w-3.5" />
        {showLabel && (
          <span className="text-xs font-medium">{currentTheme.label}</span>
        )}
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200",
            "bg-card hover:bg-accent text-foreground hover:text-accent-foreground",
            "border border-border hover:border-accent-foreground/20",
            "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
            isRTL && "flex-row-reverse",
            className
          )}
        >
          <CurrentIcon className="h-4 w-4" />
          {showLabel && (
            <span className="text-sm font-medium">{currentTheme.label}</span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align={isRTL ? "start" : "end"} 
        className="w-56 bg-popover border border-border shadow-lg"
      >
        {themes.map((themeOption) => {
          const Icon = themeOption.icon;
          return (
            <DropdownMenuItem
              key={themeOption.value}
              onClick={() => setTheme(themeOption.value)}
              className={cn(
                "flex items-center gap-3 px-3 py-2.5 cursor-pointer transition-colors",
                "hover:bg-accent hover:text-accent-foreground",
                "focus:bg-accent focus:text-accent-foreground",
                theme === themeOption.value && "bg-accent/50 text-accent-foreground",
                isRTL && "flex-row-reverse"
              )}
            >
              <Icon className="h-4 w-4" />
              <div className={cn("flex flex-col flex-1", isRTL && "text-right")}>
                <span className="text-sm font-medium">{themeOption.label}</span>
                <span className="text-xs text-muted-foreground">
                  {themeOption.description}
                </span>
              </div>
              {theme === themeOption.value && (
                <div className={cn(
                  "h-2 w-2 rounded-full bg-primary",
                  isRTL ? "mr-auto" : "ml-auto"
                )} />
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ThemeToggle;
