
import React from 'react';
import { Sparkles, TrendingUp } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useTaskStats } from '@/hooks/useData';

const WelcomeCard: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { data: taskStats } = useTaskStats();
  const currentHour = new Date().getHours();
  
  const getGreeting = () => {
    if (currentHour < 12) {
      return isRTL ? 'صباح الخير' : 'Good morning';
    } else if (currentHour < 18) {
      return isRTL ? 'مساء الخير' : 'Good afternoon';
    } else {
      return isRTL ? 'مساء الخير' : 'Good evening';
    }
  };

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-slate-50 dark:from-blue-900/30 dark:to-slate-900/30 rounded-2xl p-8 border border-blue-200 dark:border-blue-700">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-zenith-gradient opacity-10 rounded-full -translate-y-16 translate-x-16"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-zenith-gradient opacity-5 rounded-full translate-y-12 -translate-x-12"></div>
      
      <div className={cn(
        "relative flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn("space-y-4", isRTL && "text-right")}>
          <div className="space-y-2">
            <div className={cn(
              "flex items-center gap-2 text-zenith-sage-600 dark:text-zenith-sage-400",
              isRTL && "flex-row-reverse"
            )}>
              <Sparkles className="w-5 h-5" />
              <span className="text-sm font-medium">{getGreeting()}</span>
            </div>
            <h1 className="text-3xl font-bold text-foreground">
              {t('dashboard.welcome')}
            </h1>
            <p className="text-muted-foreground text-lg">
              {t('dashboard.subtitle')}
            </p>
          </div>
          
          {taskStats && taskStats.total > 0 && (
            <div className={cn(
              "flex items-center gap-2 px-4 py-2 bg-white/60 dark:bg-black/20 rounded-lg backdrop-blur-sm w-fit",
              isRTL && "flex-row-reverse"
            )}>
              <TrendingUp className="w-4 h-4 text-slate-600 dark:text-slate-400" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                {isRTL
                  ? `${taskStats.completed} مهمة مكتملة من ${taskStats.total}`
                  : `${taskStats.completed} of ${taskStats.total} tasks completed`
                }
              </span>
            </div>
          )}
        </div>
        
        <div className="hidden md:block">
          <div className="w-32 h-32 bg-zenith-gradient rounded-full flex items-center justify-center zenith-float">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <Sparkles className="w-12 h-12 text-white" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeCard;
