import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/hooks/useTheme';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import DataResetComponent from './DataResetComponent';
import {
  Settings as SettingsIcon,
  Moon,
  Sun,
  Globe,
  Bell,
  Shield,
  Database
} from 'lucide-react';

const SettingsPanel: React.FC = () => {
  const { t, language, setLanguage } = useLanguage();
  const { theme, setTheme } = useTheme();

  const settingsSections = [
    {
      title: t('settings.appearance'),
      icon: Sun,
      settings: [
        {
          id: 'theme',
          label: t('settings.theme'),
          type: 'select',
          value: theme,
          options: [
            { value: 'light', label: t('settings.light') },
            { value: 'dark', label: t('settings.dark') },
            { value: 'system', label: t('settings.system') }
          ],
          onChange: setTheme
        },
        {
          id: 'language',
          label: t('settings.language'),
          type: 'select',
          value: language,
          options: [
            { value: 'en', label: 'English' },
            { value: 'ar', label: 'العربية' }
          ],
          onChange: setLanguage
        }
      ]
    },
    {
      title: t('settings.notifications'),
      icon: Bell,
      settings: [
        {
          id: 'taskReminders',
          label: t('settings.taskReminders'),
          type: 'switch',
          value: true,
          onChange: (value: boolean) => console.log('Task reminders:', value)
        },
        {
          id: 'focusBreaks',
          label: t('settings.focusBreaks'),
          type: 'switch',
          value: true,
          onChange: (value: boolean) => console.log('Focus breaks:', value)
        }
      ]
    },
    {
      title: t('settings.privacy'),
      icon: Shield,
      settings: [
        {
          id: 'analytics',
          label: t('settings.analytics'),
          type: 'switch',
          value: false,
          onChange: (value: boolean) => console.log('Analytics:', value)
        },
        {
          id: 'dataSharing',
          label: t('settings.dataSharing'),
          type: 'switch',
          value: false,
          onChange: (value: boolean) => console.log('Data sharing:', value)
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {settingsSections.map((section, sectionIndex) => (
        <Card key={sectionIndex}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <section.icon className="w-5 h-5 mr-2" />
              {section.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {section.settings.map((setting) => (
              <div key={setting.id} className="flex items-center justify-between">
                <Label htmlFor={setting.id} className="text-sm font-medium">
                  {setting.label}
                </Label>
                
                {setting.type === 'switch' && (
                  <Switch
                    id={setting.id}
                    checked={setting.value as boolean}
                    onCheckedChange={setting.onChange}
                  />
                )}
                
                {setting.type === 'select' && (
                  <Select
                    value={setting.value as string}
                    onValueChange={setting.onChange}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {setting.options?.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      ))}

      {/* Data Management Section */}
      <DataResetComponent />
    </div>
  );
};

export default SettingsPanel;
