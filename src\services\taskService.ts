import { db } from '@/lib/database';
import type { Task, TaskFilter, PaginationOptions, PaginatedResult } from '@/types/database';
import { v4 as uuidv4 } from 'uuid';

export class TaskService {
  // Create a new task
  async createTask(taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
    try {
      const task: Task = {
        ...taskData,
        id: uuidv4(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.tasks.add(task);
      
      // Update search index
      await db.updateSearchIndex(
        'task',
        task.id,
        task.title,
        task.description || '',
        task.tags
      );

      // Log activity
      await db.logActivity(
        'current-user', // TODO: Get from auth context
        'create',
        'task',
        task.id,
        { title: task.title, priority: task.priority }
      );

      return task;
    } catch (error) {
      console.error('Error creating task:', error);
      throw new Error('Failed to create task');
    }
  }

  // Get task by ID
  async getTaskById(id: string): Promise<Task | undefined> {
    try {
      return await db.tasks.get(id);
    } catch (error) {
      console.error('Error getting task:', error);
      throw new Error('Failed to get task');
    }
  }

  // Get all tasks with optional filtering and pagination
  async getTasks(
    filter: TaskFilter = {},
    pagination: PaginationOptions = { page: 1, limit: 50 }
  ): Promise<PaginatedResult<Task>> {
    try {
      // Start with all tasks
      let collection = db.tasks.toCollection();

      // Apply filters
      if (filter.status && filter.status.length > 0) {
        collection = collection.filter(task => filter.status!.includes(task.status));
      }

      if (filter.priority && filter.priority.length > 0) {
        collection = collection.filter(task => filter.priority!.includes(task.priority));
      }

      if (filter.projectId) {
        collection = collection.filter(task => task.projectId === filter.projectId);
      }

      if (filter.assignedTo) {
        collection = collection.filter(task => task.assignedTo === filter.assignedTo);
      }

      if (filter.tags && filter.tags.length > 0) {
        collection = collection.filter(task =>
          filter.tags!.some(tag => task.tags.includes(tag))
        );
      }

      if (filter.dueDateFrom) {
        collection = collection.filter(task =>
          task.dueDate && task.dueDate >= filter.dueDateFrom!
        );
      }

      if (filter.dueDateTo) {
        collection = collection.filter(task =>
          task.dueDate && task.dueDate <= filter.dueDateTo!
        );
      }

      // Apply sorting
      const sortBy = pagination.sortBy || 'createdAt';
      const sortOrder = pagination.sortOrder || 'desc';

      if (sortOrder === 'desc') {
        collection = collection.reverse().sortBy(sortBy);
      } else {
        collection = collection.sortBy(sortBy);
      }

      // Get all filtered and sorted data
      const allData = await collection;
      const total = allData.length;

      // Apply pagination
      const offset = (pagination.page - 1) * pagination.limit;
      const data = allData.slice(offset, offset + pagination.limit);

      const totalPages = Math.ceil(total / pagination.limit);

      return {
        data,
        total,
        page: pagination.page,
        limit: pagination.limit,
        totalPages,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1
      };
    } catch (error) {
      console.error('Error getting tasks:', error);
      throw new Error('Failed to get tasks');
    }
  }

  // Update task
  async updateTask(id: string, updates: Partial<Omit<Task, 'id' | 'createdAt'>>): Promise<Task> {
    try {
      const existingTask = await db.tasks.get(id);
      if (!existingTask) {
        throw new Error('Task not found');
      }

      const updatedTask = {
        ...existingTask,
        ...updates,
        updatedAt: new Date()
      };

      await db.tasks.update(id, updatedTask);

      // Update search index if title, description, or tags changed
      if (updates.title || updates.description || updates.tags) {
        await db.updateSearchIndex(
          'task',
          id,
          updatedTask.title,
          updatedTask.description || '',
          updatedTask.tags
        );
      }

      // Log activity
      await db.logActivity(
        'current-user',
        'update',
        'task',
        id,
        { changes: updates }
      );

      return updatedTask;
    } catch (error) {
      console.error('Error updating task:', error);
      throw new Error('Failed to update task');
    }
  }

  // Delete task
  async deleteTask(id: string): Promise<void> {
    try {
      const task = await db.tasks.get(id);
      if (!task) {
        throw new Error('Task not found');
      }

      await db.transaction('rw', [db.tasks, db.searchIndex, db.comments, db.timeEntries], async () => {
        // Delete task
        await db.tasks.delete(id);
        
        // Delete from search index
        await db.searchIndex.where('entityId').equals(id).delete();
        
        // Delete related comments
        await db.comments.where('entityId').equals(id).delete();
        
        // Delete related time entries
        await db.timeEntries.where('taskId').equals(id).delete();
      });

      // Log activity
      await db.logActivity(
        'current-user',
        'delete',
        'task',
        id,
        { title: task.title }
      );
    } catch (error) {
      console.error('Error deleting task:', error);
      throw new Error('Failed to delete task');
    }
  }

  // Complete task
  async completeTask(id: string): Promise<Task> {
    try {
      const updates = {
        status: 'completed' as const,
        completedAt: new Date()
      };

      return await this.updateTask(id, updates);
    } catch (error) {
      console.error('Error completing task:', error);
      throw new Error('Failed to complete task');
    }
  }

  // Get tasks by project
  async getTasksByProject(projectId: string): Promise<Task[]> {
    try {
      return await db.tasks
        .where('projectId')
        .equals(projectId)
        .sortBy('order');
    } catch (error) {
      console.error('Error getting tasks by project:', error);
      throw new Error('Failed to get tasks by project');
    }
  }

  // Get overdue tasks
  async getOverdueTasks(): Promise<Task[]> {
    try {
      const now = new Date();
      return await db.tasks
        .where('dueDate')
        .below(now)
        .and(task => task.status !== 'completed' && task.status !== 'cancelled')
        .toArray();
    } catch (error) {
      console.error('Error getting overdue tasks:', error);
      throw new Error('Failed to get overdue tasks');
    }
  }

  // Get tasks due today
  async getTasksDueToday(): Promise<Task[]> {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

      return await db.tasks
        .where('dueDate')
        .between(startOfDay, endOfDay)
        .and(task => task.status !== 'completed' && task.status !== 'cancelled')
        .toArray();
    } catch (error) {
      console.error('Error getting tasks due today:', error);
      throw new Error('Failed to get tasks due today');
    }
  }

  // Get task statistics
  async getTaskStats(): Promise<{
    total: number;
    completed: number;
    inProgress: number;
    todo: number;
    overdue: number;
    dueToday: number;
  }> {
    try {
      const [total, completed, inProgress, todo, overdue, dueToday] = await Promise.all([
        db.tasks.count(),
        db.tasks.where('status').equals('completed').count(),
        db.tasks.where('status').equals('inProgress').count(),
        db.tasks.where('status').equals('todo').count(),
        this.getOverdueTasks().then(tasks => tasks.length),
        this.getTasksDueToday().then(tasks => tasks.length)
      ]);

      return {
        total,
        completed,
        inProgress,
        todo,
        overdue,
        dueToday
      };
    } catch (error) {
      console.error('Error getting task stats:', error);
      throw new Error('Failed to get task stats');
    }
  }

  // Reorder tasks
  async reorderTasks(taskIds: string[]): Promise<void> {
    try {
      await db.transaction('rw', db.tasks, async () => {
        for (let i = 0; i < taskIds.length; i++) {
          await db.tasks.update(taskIds[i], { order: i });
        }
      });
    } catch (error) {
      console.error('Error reordering tasks:', error);
      throw new Error('Failed to reorder tasks');
    }
  }

  // Bulk update tasks
  async bulkUpdateTasks(taskIds: string[], updates: Partial<Omit<Task, 'id' | 'createdAt'>>): Promise<void> {
    try {
      await db.transaction('rw', db.tasks, async () => {
        for (const taskId of taskIds) {
          await db.tasks.update(taskId, { ...updates, updatedAt: new Date() });
        }
      });

      // Log activity
      await db.logActivity(
        'current-user',
        'bulk_update',
        'task',
        taskIds.join(','),
        { count: taskIds.length, changes: updates }
      );
    } catch (error) {
      console.error('Error bulk updating tasks:', error);
      throw new Error('Failed to bulk update tasks');
    }
  }

  // Search tasks
  async searchTasks(query: string): Promise<Task[]> {
    try {
      const searchResults = await db.searchEntities(query, ['task']);
      const taskIds = searchResults.map(result => result.entityId);
      
      if (taskIds.length === 0) {
        return [];
      }

      return await db.tasks.where('id').anyOf(taskIds).toArray();
    } catch (error) {
      console.error('Error searching tasks:', error);
      throw new Error('Failed to search tasks');
    }
  }
}

// Export singleton instance
export const taskService = new TaskService();
