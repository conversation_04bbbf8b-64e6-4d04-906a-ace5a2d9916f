import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, Pin, Calendar, Tag, FileText, MoreVertical } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useNote, useDeleteNote, useTogglePinNote } from '@/hooks/useData';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import NoteForm from '@/components/Notes/NoteForm';

const NoteDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t, isRTL } = useLanguage();
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);

  const { data: note, isLoading, error } = useNote(id!);
  const deleteNoteMutation = useDeleteNote();
  const togglePinMutation = useTogglePinNote();

  const handleDelete = async () => {
    if (!note) return;
    try {
      await deleteNoteMutation.mutateAsync(note.id);
      navigate('/', { state: { activeSection: 'notes' } });
    } catch (error) {
      console.error('Error deleting note:', error);
    }
  };

  const handleTogglePin = async () => {
    if (!note) return;
    try {
      await togglePinMutation.mutateAsync(note.id);
    } catch (error) {
      console.error('Error toggling pin:', error);
    }
  };

  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getNoteColorClass = (color?: string) => {
    if (!color) return '';
    const colorClasses = {
      'yellow': 'bg-yellow-100 dark:bg-yellow-900/20',
      'blue': 'bg-blue-100 dark:bg-blue-900/20',
      'green': 'bg-green-100 dark:bg-green-900/20',
      'purple': 'bg-purple-100 dark:bg-purple-900/20',
      'pink': 'bg-pink-100 dark:bg-pink-900/20',
      'orange': 'bg-orange-100 dark:bg-orange-900/20',
    };
    return colorClasses[color as keyof typeof colorClasses] || '';
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
        <p className="text-muted-foreground">{t('common.loading')}</p>
      </div>
    );
  }

  if (error || !note) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <FileText className="w-16 h-16 text-muted-foreground mb-4" />
        <h2 className="text-2xl font-semibold mb-2">{t('notes.notFound')}</h2>
        <p className="text-muted-foreground mb-4">{t('notes.notFoundDescription')}</p>
        <Button onClick={() => navigate('/', { state: { activeSection: 'notes' } })}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          {t('common.back')}
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className={cn("flex items-center justify-between mb-6", isRTL && "flex-row-reverse")}>
          <div className={cn("flex items-center gap-4", isRTL && "flex-row-reverse")}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/', { state: { activeSection: 'notes' } })}
              className="p-2"
            >
              <ArrowLeft className={cn("w-5 h-5", isRTL && "rotate-180")} />
            </Button>
            <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
              <FileText className="w-6 h-6 text-primary" />
              <div>
                <div className={cn("flex items-center gap-2 text-sm text-muted-foreground mb-1", isRTL && "flex-row-reverse")}>
                  <span className="hover:text-primary cursor-pointer" onClick={() => navigate('/', { state: { activeSection: 'notes' } })}>
                    {t('notes.title')}
                  </span>
                  <span>/</span>
                  <span>{note.title}</span>
                </div>
                <h1 className="text-2xl font-bold">{t('notes.noteDetails')}</h1>
              </div>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align={isRTL ? "start" : "end"}>
              <DropdownMenuItem onClick={() => setIsEditFormOpen(true)}>
                <Edit className="w-4 h-4 mr-2" />
                {t('notes.edit')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleTogglePin}>
                <Pin className="w-4 h-4 mr-2" />
                {note.isPinned ? t('notes.unpin') : t('notes.pin')}
              </DropdownMenuItem>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Trash2 className="w-4 h-4 mr-2" />
                    {t('notes.delete')}
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{t('notes.confirmDelete')}</AlertDialogTitle>
                    <AlertDialogDescription>
                      {t('notes.confirmDeleteDescription')}
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleDelete}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {t('notes.delete')}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Note Content */}
        <div className={cn(
          "bg-background border border-border rounded-xl shadow-sm p-8",
          getNoteColorClass(note.color)
        )}>
          {/* Title and Pin */}
          <div className={cn("flex items-start justify-between mb-6", isRTL && "flex-row-reverse")}>
            <h2 className="text-3xl font-bold text-foreground flex-1">{note.title}</h2>
            {note.isPinned && (
              <Pin className="w-6 h-6 text-yellow-500 flex-shrink-0" />
            )}
          </div>

          {/* Content */}
          <div className="prose prose-lg max-w-none mb-8">
            <p className="text-foreground whitespace-pre-wrap leading-relaxed">
              {note.content}
            </p>
          </div>

          {/* Tags */}
          {note.tags.length > 0 && (
            <div className="mb-6">
              <div className={cn("flex items-center gap-2 mb-3", isRTL && "flex-row-reverse")}>
                <Tag className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">{t('notes.tags')}</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {note.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-sm">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="border-t border-border pt-6 mt-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Calendar className="w-4 h-4" />
                <span>{t('notes.created')}: {formatDate(note.createdAt)}</span>
              </div>
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Calendar className="w-4 h-4" />
                <span>{t('notes.lastModified')}: {formatDate(note.updatedAt)}</span>
              </div>
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <FileText className="w-4 h-4" />
                <span>{note.wordCount} {t('notes.wordCount')}</span>
              </div>
              {note.projectId && (
                <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                  <span>{t('notes.project')}: {note.projectId}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Edit Form Modal */}
        <NoteForm 
          isOpen={isEditFormOpen} 
          onClose={() => setIsEditFormOpen(false)}
          note={note}
        />
      </div>
    </div>
  );
};

export default NoteDetail;
