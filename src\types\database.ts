// Database entity types for Zenith Pulse Manager

export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Task extends BaseEntity {
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'todo' | 'inProgress' | 'completed' | 'cancelled';
  dueDate?: Date;
  completedAt?: Date;
  projectId?: string;
  tags: string[];
  estimatedHours?: number;
  actualHours?: number;
  assignedTo?: string;
  parentTaskId?: string;
  order: number;
}

export interface Project extends BaseEntity {
  name: string;
  description?: string;
  status: 'planning' | 'active' | 'completed' | 'cancelled' | 'onHold';
  startDate?: Date;
  endDate?: Date;
  deadline?: Date;
  progress: number; // 0-100
  color: string;
  teamMembers: string[];
  budget?: number;
  tags: string[];
  clientName?: string;
  priority: 'low' | 'medium' | 'high';
}

export interface Note extends BaseEntity {
  title: string;
  content: string;
  tags: string[];
  isPinned: boolean;
  isArchived: boolean;
  color?: string;
  projectId?: string;
  taskId?: string;
  attachments: string[];
  wordCount: number;
  lastViewedAt?: Date;
}

export interface User extends BaseEntity {
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'user' | 'viewer';
  preferences: UserPreferences;
  isActive: boolean;
}

export interface UserPreferences {
  language: 'ar' | 'en';
  theme: 'light' | 'dark' | 'system';
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  notifications: NotificationSettings;
  dashboard: DashboardSettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  taskReminders: boolean;
  projectUpdates: boolean;
  deadlineAlerts: boolean;
  reminderTime: number; // minutes before due date
}

export interface DashboardSettings {
  widgets: string[];
  layout: 'grid' | 'list';
  showCompletedTasks: boolean;
  defaultView: 'dashboard' | 'tasks' | 'projects' | 'notes';
}

export interface ActivityLog extends BaseEntity {
  userId: string;
  action: string;
  entityType: 'task' | 'project' | 'note' | 'user';
  entityId: string;
  details: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
}

export interface Tag extends BaseEntity {
  name: string;
  color: string;
  description?: string;
  category: 'task' | 'project' | 'note' | 'general';
  usageCount: number;
}

export interface Attachment extends BaseEntity {
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  entityType: 'task' | 'project' | 'note';
  entityId: string;
  uploadedBy: string;
}

export interface TimeEntry extends BaseEntity {
  taskId: string;
  projectId?: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in minutes
  description?: string;
  billable: boolean;
  hourlyRate?: number;
}

export interface Comment extends BaseEntity {
  content: string;
  entityType: 'task' | 'project' | 'note';
  entityId: string;
  userId: string;
  parentCommentId?: string;
  mentions: string[];
  attachments: string[];
  isEdited: boolean;
  editedAt?: Date;
}

export interface Reminder extends BaseEntity {
  title: string;
  description?: string;
  reminderTime: Date;
  isCompleted: boolean;
  completedAt?: Date;
  entityType?: 'task' | 'project' | 'note';
  entityId?: string;
  userId: string;
  repeatInterval?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  repeatUntil?: Date;
}

export interface SearchIndex extends BaseEntity {
  entityType: 'task' | 'project' | 'note';
  entityId: string;
  title: string;
  content: string;
  tags: string[];
  searchableText: string;
  lastIndexed: Date;
}

// Analytics and reporting types
export interface ProductivityMetric extends BaseEntity {
  userId: string;
  date: Date;
  tasksCompleted: number;
  tasksCreated: number;
  hoursWorked: number;
  projectsActive: number;
  notesCreated: number;
  productivityScore: number; // 0-100
}

export interface Goal extends BaseEntity {
  title: string;
  description?: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  deadline: Date;
  isCompleted: boolean;
  completedAt?: Date;
  userId: string;
  category: 'productivity' | 'tasks' | 'projects' | 'learning' | 'personal';
}

// Database configuration types
export interface DatabaseConfig {
  name: string;
  version: number;
  stores: Record<string, string>;
}

// Export/Import types
export interface ExportData {
  version: string;
  exportDate: Date;
  tasks: Task[];
  projects: Project[];
  notes: Note[];
  users: User[];
  tags: Tag[];
  timeEntries: TimeEntry[];
  goals: Goal[];
}

export interface ImportOptions {
  overwriteExisting: boolean;
  preserveIds: boolean;
  skipValidation: boolean;
}

// Query and filter types
export interface TaskFilter {
  status?: Task['status'][];
  priority?: Task['priority'][];
  projectId?: string;
  assignedTo?: string;
  tags?: string[];
  dueDateFrom?: Date;
  dueDateTo?: Date;
  completedDateFrom?: Date;
  completedDateTo?: Date;
}

export interface ProjectFilter {
  status?: Project['status'][];
  priority?: Project['priority'][];
  teamMember?: string;
  tags?: string[];
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
}

export interface NoteFilter {
  tags?: string[];
  isPinned?: boolean;
  isArchived?: boolean;
  projectId?: string;
  taskId?: string;
  createdDateFrom?: Date;
  createdDateTo?: Date;
}

// Pagination types
export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
