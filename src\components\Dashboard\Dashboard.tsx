
import React from 'react';
import { CheckSquare, Calendar, FileText, TrendingUp } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import WelcomeCard from './WelcomeCard';
import StatsCard from './StatsCard';
import TaskWidget from './TaskWidget';
import ProductivityChart from './ProductivityChart';
import QuickActions from './QuickActions';
import { useTaskStats, useProjectStats, useNoteStats } from '@/hooks/useData';

const Dashboard: React.FC = () => {
  const { t } = useLanguage();

  // Fetch real statistics
  const { data: taskStats, isLoading: taskStatsLoading } = useTaskStats();
  const { data: projectStats, isLoading: projectStatsLoading } = useProjectStats();
  const { data: noteStats, isLoading: noteStatsLoading } = useNoteStats();

  // Calculate productivity score based on completed tasks
  const productivityScore = taskStats ?
    Math.round((taskStats.completed / Math.max(taskStats.total, 1)) * 100) : 0;

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto bg-background min-h-screen transition-colors duration-200">
      {/* Welcome Section */}
      <div className="grid grid-cols-1 gap-6">
        <WelcomeCard />
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title={t('dashboard.completedTasks')}
          value={taskStatsLoading ? '...' : taskStats?.completed.toString() || '0'}
          subtitle={t('dashboard.today')}
          icon={CheckSquare}
          color="primary"
          progress={taskStats ? Math.round((taskStats.completed / Math.max(taskStats.total, 1)) * 100) : 0}
          trend="up"
          trendValue={taskStats?.dueToday ? `${taskStats.dueToday}` : '0'}
        />
        <StatsCard
          title={t('dashboard.activeProjects')}
          value={projectStatsLoading ? '...' : projectStats?.active.toString() || '0'}
          subtitle={t('dashboard.inProgress')}
          icon={Calendar}
          color="secondary"
          progress={projectStats ? Math.round((projectStats.active / Math.max(projectStats.total, 1)) * 100) : 0}
          trend="up"
          trendValue={projectStats?.total ? `${projectStats.total}` : '0'}
        />
        <StatsCard
          title={t('dashboard.notesCreated')}
          value={noteStatsLoading ? '...' : noteStats?.total.toString() || '0'}
          subtitle={t('dashboard.thisMonth')}
          icon={FileText}
          color="success"
          trend="up"
          trendValue={noteStats?.pinned ? `${noteStats.pinned}` : '0'}
        />
        <StatsCard
          title={t('dashboard.productivity')}
          value={taskStatsLoading ? '...' : `${productivityScore}%`}
          subtitle={t('dashboard.weeklyAverage')}
          icon={TrendingUp}
          color="neutral"
          progress={productivityScore}
          trend={productivityScore >= 70 ? "up" : productivityScore >= 40 ? "neutral" : "down"}
          trendValue={`${productivityScore}%`}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Tasks */}
        <div className="lg:col-span-2 space-y-6">
          <TaskWidget />
          <ProductivityChart />
        </div>

        {/* Right Column - Quick Actions */}
        <div className="space-y-6">
          <QuickActions />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
