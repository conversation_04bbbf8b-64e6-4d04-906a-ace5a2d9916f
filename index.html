
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Zenith Flow - Effortless Productivity</title>
    <meta name="description" content="Transform your productivity with Zenith Flow - the ultimate task and time management application with AI-powered insights" />
    <meta name="author" content="Zenith Flow Team" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <meta property="og:title" content="Zenith Flow - Effortless Productivity" />
    <meta property="og:description" content="Transform your productivity with Zenith Flow - the ultimate task and time management application with AI-powered insights" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@zenithflow" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    <style>
      /* Theme-aware background colors using OKLCH */
      html, body, #root {
        background-color: oklch(0.985 0.002 247.839) !important;
        background: oklch(0.985 0.002 247.839) !important;
        background-image: none !important;
        transition: background-color 0.3s ease !important;
      }

      /* Dark mode backgrounds */
      .dark html, .dark body, .dark #root {
        background-color: oklch(0.208 0.042 265.755) !important;
        background: oklch(0.208 0.042 265.755) !important;
        background-image: none !important;
      }

      /* Override any yellow/amber backgrounds */
      *[style*="yellow"], *[style*="amber"], *[class*="yellow"], *[class*="amber"] {
        background-color: oklch(0.985 0.002 247.839) !important;
        background: oklch(0.985 0.002 247.839) !important;
      }

      .dark *[style*="yellow"], .dark *[style*="amber"], .dark *[class*="yellow"], .dark *[class*="amber"] {
        background-color: oklch(0.208 0.042 265.755) !important;
        background: oklch(0.208 0.042 265.755) !important;
      }

      /* Ensure main container has proper background */
      .min-h-screen {
        background-color: oklch(0.985 0.002 247.839) !important;
        background: oklch(0.985 0.002 247.839) !important;
        transition: background-color 0.3s ease !important;
      }

      .dark .min-h-screen {
        background-color: oklch(0.208 0.042 265.755) !important;
        background: oklch(0.208 0.042 265.755) !important;
      }
    </style>
    <script>
      // Theme-aware background color management
      document.addEventListener('DOMContentLoaded', function() {
        function updateBackgroundColors() {
          const isDarkMode = document.documentElement.classList.contains('dark');
          const lightBg = 'oklch(0.985 0.002 247.839)';
          const darkBg = 'oklch(0.208 0.042 265.755)';
          const bgColor = isDarkMode ? darkBg : lightBg;

          document.body.style.backgroundColor = bgColor;
          document.documentElement.style.backgroundColor = bgColor;
        }

        // Initial background setup
        updateBackgroundColors();

        // Watch for theme changes
        const themeObserver = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
              updateBackgroundColors();
            }
          });
        });

        themeObserver.observe(document.documentElement, {
          attributes: true,
          attributeFilter: ['class']
        });

        // Monitor for any yellow/amber color changes and override them
        const colorObserver = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
              const target = mutation.target;
              const isDarkMode = document.documentElement.classList.contains('dark');

              if (target.style.backgroundColor &&
                  (target.style.backgroundColor.includes('yellow') ||
                   target.style.backgroundColor.includes('amber') ||
                   target.style.backgroundColor.includes('orange'))) {
                target.style.backgroundColor = isDarkMode ?
                  'oklch(0.208 0.042 265.755)' : 'oklch(0.985 0.002 247.839)';
              }
              if (target.style.borderColor &&
                  (target.style.borderColor.includes('yellow') ||
                   target.style.borderColor.includes('amber') ||
                   target.style.borderColor.includes('orange'))) {
                target.style.borderColor = isDarkMode ?
                  'oklch(0.372 0.044 257.287)' : 'oklch(0.928 0.006 264.531)';
              }
              if (target.style.color &&
                  (target.style.color.includes('yellow') ||
                   target.style.color.includes('amber') ||
                   target.style.color.includes('orange'))) {
                target.style.color = isDarkMode ?
                  'oklch(0.707 0.022 261.325)' : 'oklch(0.554 0.046 257.417)';
              }
            }
          });
        });

        colorObserver.observe(document.body, {
          attributes: true,
          subtree: true,
          attributeFilter: ['style']
        });
      });
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
