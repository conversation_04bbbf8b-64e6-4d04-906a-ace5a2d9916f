import React, { useState } from 'react';
import FocusMode from '@/components/Focus/FocusMode';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import { Timer } from 'lucide-react';

const Focus: React.FC = () => {
  const { t } = useLanguage();
  const [showFocusMode, setShowFocusMode] = useState(false);

  if (showFocusMode) {
    return <FocusMode onClose={() => setShowFocusMode(false)} />;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">
          {t('focus.title')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {t('focus.description')}
        </p>
      </div>
      
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-6">
        <div className="text-center">
          <Timer className="w-24 h-24 mx-auto mb-4 text-primary" />
          <h2 className="text-2xl font-semibold mb-2">
            {t('focus.ready')}
          </h2>
          <p className="text-muted-foreground mb-6">
            {t('focus.readyDescription')}
          </p>
        </div>
        
        <Button 
          onClick={() => setShowFocusMode(true)}
          size="lg"
          className="px-8 py-3"
        >
          {t('focus.start')}
        </Button>
      </div>
    </div>
  );
};

export default Focus;
