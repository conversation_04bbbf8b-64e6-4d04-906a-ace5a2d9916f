import React from 'react';
import NotesList from '@/components/Notes/NotesList';
import { useLanguage } from '@/contexts/LanguageContext';

const Notes: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">
          {t('notes.title')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {t('notes.description')}
        </p>
      </div>
      
      <NotesList />
    </div>
  );
};

export default Notes;
