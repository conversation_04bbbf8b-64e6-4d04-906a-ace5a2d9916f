/**
 * Theme Utilities for Zenith Flow
 * Comprehensive utilities for working with the OKLCH-based theme system
 */

import { componentThemes } from './component-themes';
import { cn } from './utils';

// Type definitions for theme utilities
export type ThemeMode = 'light' | 'dark' | 'system';
export type ComponentVariant = 'default' | 'primary' | 'secondary' | 'destructive' | 'outline' | 'ghost';
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * Generate theme-aware CSS classes for components
 */
export const themeClasses = {
  // Button classes with theme support
  button: (variant: ComponentVariant = 'default', size: ComponentSize = 'md') => {
    const baseClasses = [
      'inline-flex items-center justify-center gap-2',
      'font-medium transition-all duration-200 ease-in-out',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:pointer-events-none disabled:opacity-50',
    ];

    const variantClasses = {
      default: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-ring',
      primary: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-ring',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-ring',
      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 focus:ring-ring',
      outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-ring',
      ghost: 'hover:bg-accent hover:text-accent-foreground focus:ring-ring',
    };

    const sizeClasses = {
      xs: 'h-7 px-2 text-xs rounded-md',
      sm: 'h-8 px-3 text-sm rounded-md',
      md: 'h-9 px-4 text-sm rounded-md',
      lg: 'h-10 px-6 text-base rounded-md',
      xl: 'h-11 px-8 text-base rounded-lg',
    };

    return cn(baseClasses, variantClasses[variant], sizeClasses[size]);
  },

  // Card classes with theme support
  card: (variant: 'default' | 'elevated' | 'outlined' | 'interactive' | 'muted' = 'default') => {
    const baseClasses = [
      'rounded-lg border bg-card text-card-foreground',
      'transition-all duration-200 ease-in-out',
    ];

    const variantClasses = {
      default: 'border-border shadow-sm',
      elevated: 'border-border shadow-lg',
      outlined: 'border-2 border-border bg-transparent shadow-none',
      interactive: 'border-border shadow-sm hover:shadow-md hover:-translate-y-0.5 cursor-pointer hover:border-ring',
      muted: 'bg-muted text-muted-foreground border-border',
    };

    return cn(baseClasses, variantClasses[variant]);
  },

  // Input classes with theme support
  input: (state: 'default' | 'error' | 'disabled' = 'default') => {
    const baseClasses = [
      'flex h-9 w-full rounded-md border px-3 py-1 text-sm',
      'bg-background text-foreground transition-colors',
      'file:border-0 file:bg-transparent file:text-sm file:font-medium',
      'placeholder:text-muted-foreground',
      'focus-visible:outline-none focus-visible:ring-1',
    ];

    const stateClasses = {
      default: 'border-input focus-visible:ring-ring hover:border-ring/50',
      error: 'border-destructive focus-visible:ring-destructive',
      disabled: 'cursor-not-allowed opacity-50 bg-muted',
    };

    return cn(baseClasses, stateClasses[state]);
  },

  // Badge classes with theme support
  badge: (variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'muted' = 'default') => {
    const baseClasses = [
      'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold',
      'transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    ];

    const variantClasses = {
      default: 'bg-primary text-primary-foreground hover:bg-primary/80',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/80',
      outline: 'border border-input text-foreground hover:bg-accent hover:text-accent-foreground',
      muted: 'bg-muted text-muted-foreground hover:bg-muted/80',
    };

    return cn(baseClasses, variantClasses[variant]);
  },

  // Navigation item classes with theme support
  navItem: (isActive: boolean = false) => {
    const baseClasses = [
      'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium',
      'transition-all duration-200 ease-in-out cursor-pointer',
    ];

    const stateClasses = isActive
      ? 'bg-accent text-accent-foreground border-l-2 border-primary'
      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground';

    return cn(baseClasses, stateClasses);
  },

  // Modal/Dialog classes with theme support
  modal: {
    overlay: 'fixed inset-0 z-50 bg-background/80 backdrop-blur-sm transition-all duration-200',
    content: cn(
      'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%]',
      'gap-4 border bg-background p-6 shadow-lg duration-200 rounded-lg',
      'data-[state=open]:animate-in data-[state=closed]:animate-out',
      'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
      'data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%]',
      'data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]'
    ),
  },

  // Tooltip classes with theme support
  tooltip: cn(
    'z-50 overflow-hidden rounded-md bg-popover px-3 py-1.5 text-xs text-popover-foreground',
    'shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out',
    'data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95',
    'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2',
    'data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2'
  ),
};

/**
 * CSS custom property helpers
 */
export const cssVars = {
  // Get CSS custom property value
  get: (property: string): string => {
    if (typeof window !== 'undefined') {
      return getComputedStyle(document.documentElement).getPropertyValue(property);
    }
    return '';
  },

  // Set CSS custom property value
  set: (property: string, value: string): void => {
    if (typeof window !== 'undefined') {
      document.documentElement.style.setProperty(property, value);
    }
  },

  // Get OKLCH color from CSS variable
  getOKLCH: (cssVar: string): string => {
    const value = cssVars.get(cssVar);
    return value ? `oklch(${value})` : '';
  },

  // Theme color shortcuts
  colors: {
    background: () => cssVars.getOKLCH('--background'),
    foreground: () => cssVars.getOKLCH('--foreground'),
    card: () => cssVars.getOKLCH('--card'),
    cardForeground: () => cssVars.getOKLCH('--card-foreground'),
    popover: () => cssVars.getOKLCH('--popover'),
    popoverForeground: () => cssVars.getOKLCH('--popover-foreground'),
    primary: () => cssVars.getOKLCH('--primary'),
    primaryForeground: () => cssVars.getOKLCH('--primary-foreground'),
    secondary: () => cssVars.getOKLCH('--secondary'),
    secondaryForeground: () => cssVars.getOKLCH('--secondary-foreground'),
    muted: () => cssVars.getOKLCH('--muted'),
    mutedForeground: () => cssVars.getOKLCH('--muted-foreground'),
    accent: () => cssVars.getOKLCH('--accent'),
    accentForeground: () => cssVars.getOKLCH('--accent-foreground'),
    destructive: () => cssVars.getOKLCH('--destructive'),
    destructiveForeground: () => cssVars.getOKLCH('--destructive-foreground'),
    border: () => cssVars.getOKLCH('--border'),
    input: () => cssVars.getOKLCH('--input'),
    ring: () => cssVars.getOKLCH('--ring'),
  },
};

/**
 * Theme transition utilities
 */
export const themeTransitions = {
  // Disable transitions during theme change
  disable: () => {
    if (typeof window !== 'undefined') {
      document.documentElement.classList.add('theme-transition-disable');
    }
  },

  // Re-enable transitions after theme change
  enable: () => {
    if (typeof window !== 'undefined') {
      document.documentElement.classList.remove('theme-transition-disable');
      // Small delay to ensure the class is removed before transitions resume
      setTimeout(() => {
        document.documentElement.classList.remove('theme-transition-disable');
      }, 50);
    }
  },
};

/**
 * Accessibility helpers for themes
 */
export const a11y = {
  // Check if color combination meets WCAG contrast requirements
  checkContrast: (foreground: string, background: string): boolean => {
    // This is a simplified check - in production, you'd want a more robust contrast calculation
    // For OKLCH colors, we can use the lightness values
    const getForegroundLightness = (color: string): number => {
      const match = color.match(/oklch\(([0-9.]+)/);
      return match ? parseFloat(match[1]) : 0.5;
    };

    const getBackgroundLightness = (color: string): number => {
      const match = color.match(/oklch\(([0-9.]+)/);
      return match ? parseFloat(match[1]) : 0.5;
    };

    const fgLightness = getForegroundLightness(foreground);
    const bgLightness = getBackgroundLightness(background);
    const contrast = Math.abs(fgLightness - bgLightness);

    // WCAG AA requires a contrast ratio of at least 4.5:1 for normal text
    // This is a simplified approximation using lightness difference
    return contrast >= 0.4;
  },

  // Get appropriate text color for background
  getTextColor: (backgroundColor: string): string => {
    const match = backgroundColor.match(/oklch\(([0-9.]+)/);
    if (match) {
      const lightness = parseFloat(match[1]);
      return lightness > 0.6 ? cssVars.colors.foreground() : cssVars.colors.background();
    }
    return cssVars.colors.foreground();
  },
};

export default {
  themeClasses,
  cssVars,
  themeTransitions,
  a11y,
};
