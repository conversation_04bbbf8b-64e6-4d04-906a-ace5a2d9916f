import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Plus, FileText, Pin, Calendar, StickyNote, Edit, Trash2, MoreVertical, ExternalLink } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useNotes, useDeleteNote, useTogglePinNote } from '@/hooks/useData';
import { cn } from '@/lib/utils';
import NoteForm from './NoteForm';
import type { Note } from '@/types/database';

const NotesList: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingNote, setEditingNote] = useState<Note | null>(null);

  // Fetch real notes data
  const { data: notesResult, isLoading, error } = useNotes({}, { page: 1, limit: 50 });
  const deleteNoteMutation = useDeleteNote();
  const togglePinMutation = useTogglePinNote();

  const notes = notesResult?.data || [];

  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleEditNote = (note: Note) => {
    setEditingNote(note);
    setIsFormOpen(true);
  };

  const handleDeleteNote = async (noteId: string) => {
    try {
      await deleteNoteMutation.mutateAsync(noteId);
    } catch (error) {
      console.error('Error deleting note:', error);
    }
  };

  const handleTogglePin = async (noteId: string) => {
    try {
      await togglePinMutation.mutateAsync(noteId);
    } catch (error) {
      console.error('Error toggling pin:', error);
    }
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingNote(null);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-muted rounded w-48"></div>
          <div className="h-10 bg-muted rounded w-32"></div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="h-48 bg-muted rounded-xl"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-destructive">{t('common.error')}</p>
          <p className="text-muted-foreground mt-2">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">{t('notes.allNotes')}</h2>
        <Button onClick={() => setIsFormOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          {t('notes.newNote')}
        </Button>
      </div>

      {notes.length === 0 ? (
        <div className="text-center py-12">
          <StickyNote className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">{t('notes.empty.title')}</h3>
          <p className="text-muted-foreground mb-4">{t('notes.empty.description')}</p>
          <Button onClick={() => setIsFormOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            {t('notes.newNote')}
          </Button>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {notes.map((note) => {
            const noteColorClass = note.color ? {
              'yellow': 'bg-yellow-100 dark:bg-yellow-900/20',
              'blue': 'bg-blue-100 dark:bg-blue-900/20',
              'green': 'bg-green-100 dark:bg-green-900/20',
              'purple': 'bg-purple-100 dark:bg-purple-900/20',
              'pink': 'bg-pink-100 dark:bg-pink-900/20',
              'orange': 'bg-orange-100 dark:bg-orange-900/20',
            }[note.color] : '';

            return (
              <Card
                key={note.id}
                className={cn("hover:shadow-md transition-shadow cursor-pointer", noteColorClass)}
                onClick={() => navigate(`/notes/${note.id}`)}
              >
                <CardHeader className="pb-3">
                  <div className={cn("flex items-start justify-between", isRTL && "flex-row-reverse")}>
                    <div className={cn("flex items-center space-x-2 flex-1", isRTL && "space-x-reverse")}>
                      <FileText className="w-5 h-5 text-primary flex-shrink-0" />
                      <CardTitle className="text-lg line-clamp-1">{note.title}</CardTitle>
                    </div>
                    <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                      {note.isPinned && (
                        <Pin className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                      )}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align={isRTL ? "start" : "end"}>
                          <DropdownMenuItem onClick={() => navigate(`/notes/${note.id}`)}>
                            <ExternalLink className="w-4 h-4 mr-2" />
                            {t('notes.viewDetails')}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditNote(note)}>
                            <Edit className="w-4 h-4 mr-2" />
                            {t('notes.edit')}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleTogglePin(note.id)}>
                            <Pin className="w-4 h-4 mr-2" />
                            {note.isPinned ? t('notes.unpin') : t('notes.pin')}
                          </DropdownMenuItem>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <Trash2 className="w-4 h-4 mr-2" />
                                {t('notes.delete')}
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>{t('notes.confirmDelete')}</AlertDialogTitle>
                                <AlertDialogDescription>
                                  {t('notes.confirmDeleteDescription')}
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteNote(note.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  {t('notes.delete')}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {note.content}
                  </p>

                  {note.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {note.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {note.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{note.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      {formatDate(note.updatedAt)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {note.wordCount} {t('notes.wordCount')}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {notes.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">{t('notes.noNotes')}</h3>
          <p className="text-muted-foreground mb-4">{t('notes.noNotesDescription')}</p>
          <Button onClick={() => setIsFormOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            {t('notes.createFirst')}
          </Button>
        </div>
      )}

      {/* Note Form Modal */}
      <NoteForm
        isOpen={isFormOpen}
        onClose={handleCloseForm}
        note={editingNote || undefined}
      />
    </div>
  );
};

export default NotesList;
