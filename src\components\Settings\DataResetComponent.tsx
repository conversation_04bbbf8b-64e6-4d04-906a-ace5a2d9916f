import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { 
  AlertTriangle, 
  Database, 
  Loader2, 
  CheckCircle, 
  XCircle,
  BarChart3,
  Settings as SettingsIcon
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { dataResetService, type DataResetProgress, type DataResetResult } from '@/services/dataResetService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Alert, 
  AlertDescription, 
  AlertTitle 
} from '@/components/ui/alert';
import { cn } from '@/lib/utils';

interface DataResetComponentProps {
  className?: string;
}

const DataResetComponent: React.FC<DataResetComponentProps> = ({ className }) => {
  const { t, isRTL } = useLanguage();
  const queryClient = useQueryClient();
  
  // State management
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [preserveSettings, setPreserveSettings] = useState(true);
  const [resetProgress, setResetProgress] = useState<DataResetProgress | null>(null);
  const [resetResult, setResetResult] = useState<DataResetResult | null>(null);
  const [dataStats, setDataStats] = useState<Record<string, number>>({});
  const [isLoadingStats, setIsLoadingStats] = useState(false);

  // Load data statistics on component mount
  useEffect(() => {
    loadDataStatistics();
  }, []);

  const loadDataStatistics = async () => {
    setIsLoadingStats(true);
    try {
      const stats = await dataResetService.getDataStatistics();
      setDataStats(stats);
    } catch (error) {
      console.error('Failed to load data statistics:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  const handleResetClick = () => {
    setIsDialogOpen(true);
    setResetResult(null);
    setResetProgress(null);
  };

  const handleConfirmReset = async () => {
    setIsResetting(true);
    setResetProgress(null);
    setResetResult(null);

    try {
      // Initialize the data reset service with query client
      const resetService = new (await import('@/services/dataResetService')).DataResetService(queryClient);
      
      const result = await resetService.resetAllData(
        {
          clearCache: true,
          clearLocalStorage: true,
          clearSessionStorage: true,
          preserveSettings
        },
        (progress) => {
          setResetProgress(progress);
        }
      );

      setResetResult(result);
      
      if (result.success) {
        // Reload data statistics after successful reset
        await loadDataStatistics();
      }
    } catch (error) {
      setResetResult({
        success: false,
        message: t('settings.resetError'),
        error: error instanceof Error ? error.message : 'Unknown error',
        clearedTables: []
      });
    } finally {
      setIsResetting(false);
    }
  };

  const handleCloseDialog = () => {
    if (!isResetting) {
      setIsDialogOpen(false);
      setResetProgress(null);
      setResetResult(null);
    }
  };

  const getTotalDataCount = () => {
    return Object.values(dataStats).reduce((sum, count) => sum + count, 0);
  };

  const getProgressPercentage = () => {
    if (!resetProgress) return 0;
    return Math.round((resetProgress.progress / resetProgress.total) * 100);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Data Statistics Card */}
      <Card>
        <CardHeader>
          <CardTitle className={cn(
            "flex items-center gap-2 text-lg font-semibold",
            isRTL && "flex-row-reverse"
          )}>
            <BarChart3 className="w-5 h-5 text-primary" />
            {t('settings.dataStats')}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {t('settings.dataStatsDescription')}
          </p>
        </CardHeader>
        <CardContent>
          {isLoadingStats ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(dataStats).map(([key, count]) => (
                <div key={key} className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-primary">{count}</div>
                  <div className="text-xs text-muted-foreground capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {getTotalDataCount() > 0 && (
            <div className="mt-4 p-3 bg-primary/10 rounded-lg">
              <div className="text-sm font-medium text-primary">
                Total items: {getTotalDataCount()}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Data Reset Card */}
      <Card>
        <CardHeader>
          <CardTitle className={cn(
            "flex items-center gap-2 text-lg font-semibold text-destructive",
            isRTL && "flex-row-reverse"
          )}>
            <Database className="w-5 h-5" />
            {t('settings.resetData')}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {t('settings.resetDescription')}
          </p>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>{t('common.warning')}</AlertTitle>
            <AlertDescription>
              {t('settings.resetConfirmMessage')}
            </AlertDescription>
          </Alert>
          
          <Button 
            variant="destructive" 
            onClick={handleResetClick}
            disabled={isResetting}
            className={cn(
              "w-full sm:w-auto",
              isRTL && "font-arabic"
            )}
          >
            {isResetting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                {t('settings.resetInProgress')}
              </>
            ) : (
              <>
                <Database className="w-4 h-4 mr-2" />
                {t('settings.reset')}
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Reset Confirmation Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={handleCloseDialog}>
        <DialogContent className={cn(
          "sm:max-w-md",
          isRTL && "text-right"
        )}>
          <DialogHeader>
            <DialogTitle className={cn(
              "flex items-center gap-2 text-destructive",
              isRTL && "flex-row-reverse"
            )}>
              <AlertTriangle className="w-5 h-5" />
              {t('settings.resetConfirmTitle')}
            </DialogTitle>
            <DialogDescription className="space-y-3">
              <p>{t('settings.resetConfirmMessage')}</p>
              <p className="font-medium text-destructive">
                {t('settings.resetConfirmWarning')}
              </p>
            </DialogDescription>
          </DialogHeader>

          {/* Progress Section */}
          {(isResetting || resetProgress) && (
            <div className="space-y-3">
              <Progress value={getProgressPercentage()} className="w-full" />
              {resetProgress && (
                <div className="text-sm text-muted-foreground">
                  {t(`settings.resetStep.${resetProgress.step}`) || resetProgress.message}
                </div>
              )}
            </div>
          )}

          {/* Result Section */}
          {resetResult && (
            <Alert className={resetResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              {resetResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertTitle className={resetResult.success ? "text-green-800" : "text-red-800"}>
                {resetResult.success ? t('common.success') : t('common.error')}
              </AlertTitle>
              <AlertDescription className={resetResult.success ? "text-green-700" : "text-red-700"}>
                {resetResult.message}
                {resetResult.error && (
                  <div className="mt-1 text-xs opacity-75">
                    {resetResult.error}
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Settings Preservation Option */}
          {!resetResult && (
            <div className={cn(
              "flex items-center space-x-2",
              isRTL && "space-x-reverse flex-row-reverse"
            )}>
              <Checkbox
                id="preserve-settings"
                checked={preserveSettings}
                onCheckedChange={setPreserveSettings}
                disabled={isResetting}
              />
              <label
                htmlFor="preserve-settings"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {t('settings.resetPreserveSettings')}
              </label>
            </div>
          )}

          <DialogFooter className={cn(
            "gap-2",
            isRTL && "flex-row-reverse"
          )}>
            <Button
              variant="outline"
              onClick={handleCloseDialog}
              disabled={isResetting}
            >
              {resetResult ? t('common.close') : t('common.cancel')}
            </Button>
            {!resetResult && (
              <Button
                variant="destructive"
                onClick={handleConfirmReset}
                disabled={isResetting}
              >
                {isResetting ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    {t('settings.resetInProgress')}
                  </>
                ) : (
                  t('common.confirm')
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DataResetComponent;
