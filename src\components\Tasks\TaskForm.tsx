import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { X, Save, CheckSquare, Calendar, Flag, Tag, User, Clock } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCreateTask, useUpdateTask, useProjects } from '@/hooks/useData';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import type { Task } from '@/types/database';

// Form validation schema
const taskSchema = z.object({
  title: z.string().min(1, 'عنوان المهمة مطلوب').max(200, 'العنوان طويل جداً'),
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  status: z.enum(['todo', 'inProgress', 'completed', 'cancelled']).default('todo'),
  dueDate: z.string().optional(),
  completedAt: z.string().optional(),
  projectId: z.string().optional(),
  tags: z.array(z.string()).default([]),
  estimatedHours: z.number().optional(),
  actualHours: z.number().optional(),
  assignedTo: z.string().optional(),
  parentTaskId: z.string().optional(),
  order: z.number().default(0)
});

type TaskFormData = z.infer<typeof taskSchema>;

interface TaskFormProps {
  isOpen: boolean;
  onClose: () => void;
  task?: Task; // For editing existing task
  projectId?: string; // For creating task in specific project
}

const TaskForm: React.FC<TaskFormProps> = ({ 
  isOpen, 
  onClose, 
  task,
  projectId 
}) => {
  const { t, isRTL } = useLanguage();
  const { priority } = useColors();
  const [tagInput, setTagInput] = useState('');

  const isEditing = !!task;
  const createTaskMutation = useCreateTask();
  const updateTaskMutation = useUpdateTask();
  const { data: projectsResult } = useProjects({}, { page: 1, limit: 100 });
  const projects = projectsResult?.data || [];

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: task?.title || '',
      description: task?.description || '',
      priority: task?.priority || 'medium',
      status: task?.status || 'todo',
      dueDate: task?.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '',
      completedAt: task?.completedAt ? new Date(task.completedAt).toISOString().split('T')[0] : '',
      projectId: task?.projectId || projectId || '',
      tags: task?.tags || [],
      estimatedHours: task?.estimatedHours || undefined,
      actualHours: task?.actualHours || undefined,
      assignedTo: task?.assignedTo || '',
      parentTaskId: task?.parentTaskId || '',
      order: task?.order || 0
    }
  });

  const watchedTags = watch('tags');
  const watchedStatus = watch('status');
  const watchedPriority = watch('priority');

  // Initialize form with task data when editing
  useEffect(() => {
    if (task && isOpen) {
      setValue('title', task.title);
      setValue('description', task.description || '');
      setValue('priority', task.priority);
      setValue('status', task.status);
      setValue('dueDate', task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '');
      setValue('completedAt', task.completedAt ? new Date(task.completedAt).toISOString().split('T')[0] : '');
      setValue('projectId', task.projectId || '');
      setValue('tags', task.tags);
      setValue('estimatedHours', task.estimatedHours);
      setValue('actualHours', task.actualHours);
      setValue('assignedTo', task.assignedTo || '');
      setValue('parentTaskId', task.parentTaskId || '');
      setValue('order', task.order);
    }
  }, [task, isOpen, setValue]);

  const statusOptions = [
    { value: 'todo', label: t('tasks.status.todo'), color: 'bg-gray-100 text-gray-800' },
    { value: 'inProgress', label: t('tasks.status.inProgress'), color: 'bg-blue-100 text-blue-800' },
    { value: 'completed', label: t('tasks.status.completed'), color: 'bg-green-100 text-green-800' },
    { value: 'cancelled', label: t('tasks.status.cancelled'), color: 'bg-red-100 text-red-800' }
  ];

  const priorityOptions = [
    { value: 'low', label: t('tasks.priority.low'), color: priority.low.className },
    { value: 'medium', label: t('tasks.priority.medium'), color: priority.medium.className },
    { value: 'high', label: t('tasks.priority.high'), color: priority.high.className }
  ];

  const handleAddTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      setValue('tags', [...watchedTags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const onSubmit = async (data: TaskFormData) => {
    try {
      const taskData = {
        ...data,
        dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
        completedAt: data.completedAt ? new Date(data.completedAt) : undefined,
      };

      if (isEditing && task) {
        await updateTaskMutation.mutateAsync({
          id: task.id,
          updates: taskData
        });
      } else {
        await createTaskMutation.mutateAsync(taskData);
      }
      reset();
      onClose();
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} task:`, error);
    }
  };

  const handleClose = () => {
    reset();
    setTagInput('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background border border-border rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
            <CheckSquare className="w-6 h-6 text-primary" />
            <h2 className="text-xl font-semibold">
              {isEditing ? t('tasks.edit') : t('tasks.newTask')}
            </h2>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-accent rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Title */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              {t('tasks.title')} *
            </label>
            <input
              {...register('title')}
              type="text"
              placeholder={t('tasks.titlePlaceholder')}
              className={cn(
                "w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20",
                errors.title && "border-destructive"
              )}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title.message}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              {t('tasks.description')}
            </label>
            <textarea
              {...register('description')}
              rows={4}
              placeholder={t('tasks.descriptionPlaceholder')}
              className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Priority and Status */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('tasks.priority')}
                  </label>
                  <select
                    {...register('priority')}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  >
                    {priorityOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('tasks.status')}
                  </label>
                  <select
                    {...register('status')}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  >
                    {statusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Project Selection */}
              {projects.length > 0 && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('tasks.project')}
                  </label>
                  <select
                    {...register('projectId')}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  >
                    <option value="">{t('tasks.noProject')}</option>
                    {projects.map((project) => (
                      <option key={project.id} value={project.id}>
                        {project.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Assigned To */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  {t('tasks.assignedTo')}
                </label>
                <input
                  {...register('assignedTo')}
                  type="text"
                  placeholder={t('tasks.assignedToPlaceholder')}
                  className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Due Date */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  {t('tasks.dueDate')}
                </label>
                <input
                  {...register('dueDate')}
                  type="date"
                  className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div>

              {/* Hours */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('tasks.estimatedHours')}
                  </label>
                  <input
                    {...register('estimatedHours', { valueAsNumber: true })}
                    type="number"
                    min="0"
                    step="0.5"
                    placeholder="0"
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t('tasks.actualHours')}
                  </label>
                  <input
                    {...register('actualHours', { valueAsNumber: true })}
                    type="number"
                    min="0"
                    step="0.5"
                    placeholder="0"
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              {t('tasks.tags')}
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={t('tasks.addTag')}
                className="flex-1 px-4 py-2 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
              />
              <button
                type="button"
                onClick={handleAddTag}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Tag className="w-4 h-4" />
              </button>
            </div>
            {watchedTags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {watchedTags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-3 py-1 bg-muted text-muted-foreground rounded-full text-sm"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="hover:text-destructive"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>
        </form>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-border">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            {t('common.cancel')}
          </button>
          <button
            onClick={handleSubmit(onSubmit)}
            disabled={isSubmitting || createTaskMutation.isPending || updateTaskMutation.isPending}
            className={cn(
              "flex items-center gap-2 px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",
              (isSubmitting || createTaskMutation.isPending || updateTaskMutation.isPending) && "opacity-50 cursor-not-allowed"
            )}
          >
            <Save className="w-4 h-4" />
            {(isSubmitting || createTaskMutation.isPending || updateTaskMutation.isPending) 
              ? t('common.saving') 
              : t('common.save')
            }
          </button>
        </div>
      </div>
    </div>
  );
};

export default TaskForm;
