import React from 'react';
import AnalyticsOverview from '@/components/Analytics/AnalyticsOverview';
import { useLanguage } from '@/contexts/LanguageContext';

const Analytics: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">
          {t('analytics.title')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {t('analytics.description')}
        </p>
      </div>
      
      <AnalyticsOverview />
    </div>
  );
};

export default Analytics;
