import React, { useMemo } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  TrendingUp,
  Clock,
  CheckCircle,
  Target,
  Calendar
} from 'lucide-react';
import { useTaskStats, useProjectStats, useNoteStats } from '@/hooks/useData';

const AnalyticsOverview: React.FC = () => {
  const { t, isRTL } = useLanguage();

  // Fetch real data
  const { data: taskStats } = useTaskStats();
  const { data: projectStats } = useProjectStats();
  const { data: noteStats } = useNoteStats();

  // Calculate real analytics
  const analytics = useMemo(() => {
    if (!taskStats || !projectStats) {
      return {
        tasksCompleted: 0,
        totalTasks: 0,
        projectsActive: 0,
        totalProjects: 0,
        productivity: 0,
        notesTotal: 0,
        focusTime: 0,
        weeklyGoal: 0,
        weeklyProgress: 0
      };
    }

    const productivity = taskStats.total > 0 ?
      Math.round((taskStats.completed / taskStats.total) * 100) : 0;

    return {
      tasksCompleted: taskStats.completed,
      totalTasks: taskStats.total,
      projectsActive: projectStats.active,
      totalProjects: projectStats.total,
      productivity,
      notesTotal: noteStats?.total || 0,
      focusTime: taskStats.completed * 30, // Estimate 30 minutes per completed task
      weeklyGoal: Math.max(taskStats.total, 10), // Set goal based on total tasks or minimum 10
      weeklyProgress: taskStats.completed
    };
  }, [taskStats, projectStats, noteStats]);

  const stats = [
    {
      title: t('analytics.tasksCompleted'),
      value: analytics.tasksCompleted,
      total: analytics.totalTasks,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: t('analytics.focusTime'),
      value: `${Math.floor(analytics.focusTime / 60)}h ${analytics.focusTime % 60}m`,
      icon: Clock,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: t('analytics.productivity'),
      value: `${analytics.productivity}%`,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: t('analytics.weeklyGoal'),
      value: analytics.weeklyProgress,
      total: analytics.weeklyGoal,
      icon: Target,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${stat.bgColor}`}>
                <stat.icon className={`w-4 h-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {typeof stat.value === 'number' && stat.total ? 
                  `${stat.value}/${stat.total}` : 
                  stat.value
                }
              </div>
              {stat.total && (
                <div className="mt-2">
                  <Progress 
                    value={(stat.value as number / stat.total) * 100} 
                    className="h-2"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              {t('analytics.weeklyProgress')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                <div key={day} className="flex items-center space-x-3">
                  <span className="w-8 text-sm text-muted-foreground">{day}</span>
                  <div className="flex-1">
                    <Progress 
                      value={Math.random() * 100} 
                      className="h-3"
                    />
                  </div>
                  <span className="text-sm font-medium">
                    {Math.floor(Math.random() * 10) + 1}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              {t('analytics.recentActivity')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Completed "Website Design"</p>
                  <p className="text-xs text-muted-foreground">2 hours ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Started focus session</p>
                  <p className="text-xs text-muted-foreground">4 hours ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Created new project</p>
                  <p className="text-xs text-muted-foreground">1 day ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsOverview;
