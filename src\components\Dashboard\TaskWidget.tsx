
import React from 'react';
import { CheckSquare, Clock, AlertCircle, Plus } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { useTasksDueToday, useCompleteTask } from '@/hooks/useData';
import type { Task } from '@/types/database';

const TaskWidget: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { priority } = useColors();

  const { data: tasks = [], isLoading, error } = useTasksDueToday();
  const completeTaskMutation = useCompleteTask();

  const handleToggleComplete = async (taskId: string, isCompleted: boolean) => {
    if (!isCompleted) {
      try {
        await completeTaskMutation.mutateAsync(taskId);
      } catch (error) {
        console.error('Failed to complete task:', error);
      }
    }
  };

  const getPriorityColor = (priorityLevel: string) => {
    switch (priorityLevel) {
      case 'high': return priority.high.className;
      case 'medium': return priority.medium.className;
      case 'low': return priority.low.className;
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return AlertCircle;
      case 'medium': return Clock;
      case 'low': return CheckSquare;
      default: return CheckSquare;
    }
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return new Intl.DateTimeFormat(isRTL ? 'ar' : 'en', {
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  const completedTasks = tasks.filter(task => task.status === 'completed').length;
  const totalTasks = tasks.length;
  const completionPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  if (isLoading) {
    return (
      <div className="bg-card border border-border rounded-xl p-6 transition-colors duration-200">
        <div className="animate-pulse">
          <div className="h-6 bg-muted rounded w-1/3 mb-6"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-card border border-border rounded-xl p-6 transition-colors duration-200">
        <div className="text-center text-muted-foreground">
          <p>{t('common.error')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card border border-border rounded-xl p-6 transition-colors duration-200">
      <div className={cn(
        "flex items-center justify-between mb-6",
        isRTL && "flex-row-reverse"
      )}>
        <h2 className="text-xl font-bold">{t('dashboard.todayTasks')}</h2>
        <button className="flex items-center gap-2 px-3 py-1.5 text-sm bg-zenith-gradient text-white rounded-lg hover:shadow-zenith transition-all duration-200">
          <Plus className="w-4 h-4" />
          {t('tasks.newTask')}
        </button>
      </div>

      <div className="space-y-3">
        {tasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>{t('tasks.noTasksDueToday')}</p>
          </div>
        ) : (
          tasks.map((task) => {
            const PriorityIcon = getPriorityIcon(task.priority);
            const isCompleted = task.status === 'completed';

            return (
              <div
                key={task.id}
                className={cn(
                  "flex items-center gap-4 p-4 rounded-lg border transition-all duration-200 group hover:shadow-sm",
                  isCompleted
                    ? "bg-muted/30 border-muted"
                    : "bg-background border-border hover:border-blue-200",
                  isRTL && "flex-row-reverse"
                )}
              >
                <button
                  onClick={() => handleToggleComplete(task.id, isCompleted)}
                  disabled={completeTaskMutation.isPending}
                  className={cn(
                    "flex-shrink-0 w-5 h-5 rounded border-2 transition-colors duration-200 flex items-center justify-center",
                    isCompleted
                      ? "bg-blue-500 border-blue-500 text-white"
                      : "border-muted-foreground hover:border-blue-400",
                    completeTaskMutation.isPending && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {isCompleted && <CheckSquare className="w-3 h-3" />}
                </button>

                <div className={cn("flex-1 min-w-0", isRTL && "text-right")}>
                  <p className={cn(
                    "font-medium truncate",
                    isCompleted ? "line-through text-muted-foreground" : "text-foreground"
                  )}>
                    {task.title}
                  </p>
                  <div className={cn(
                    "flex items-center gap-2 mt-1 text-xs text-muted-foreground",
                    isRTL && "flex-row-reverse"
                  )}>
                    <Clock className="w-3 h-3" />
                    <span>{formatDate(task.dueDate)}</span>
                  </div>
                </div>

                <div className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                  getPriorityColor(task.priority)
                )}>
                  <PriorityIcon className="w-3 h-3" />
                  <span>{t(`tasks.priority.${task.priority}`)}</span>
                </div>
              </div>
            );
          })
        )}
      </div>

      {totalTasks > 0 && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className={cn(
            "flex items-center justify-between text-sm text-muted-foreground",
            isRTL && "flex-row-reverse"
          )}>
            <span>
              {isRTL
                ? `${completedTasks} من ${totalTasks} مهام مكتملة`
                : `${completedTasks} of ${totalTasks} tasks completed`
              }
            </span>
            <span>{completionPercentage}%</span>
          </div>
          <div className="w-full bg-muted rounded-full h-2 mt-2">
            <div
              className="bg-zenith-sage-500 h-full rounded-full transition-all duration-500"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskWidget;
