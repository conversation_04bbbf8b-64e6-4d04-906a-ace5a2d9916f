import React from 'react';
import ProjectsList from '@/components/Projects/ProjectsList';
import { useLanguage } from '@/contexts/LanguageContext';

const Projects: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">
          {t('projects.title')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {t('projects.description')}
        </p>
      </div>
      
      <ProjectsList />
    </div>
  );
};

export default Projects;
