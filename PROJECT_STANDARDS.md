# Zenith Pulse Manager - Project Standards

## Overview
This document defines the coding standards, architectural patterns, and best practices for the Zenith Pulse Manager application. All development should follow these guidelines to ensure consistency, maintainability, and scalability.

## Technology Stack

### Core Technologies
- **Frontend Framework**: React 18+ with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with OKLCH color system
- **UI Components**: shadcn/ui components
- **Database**: IndexedDB with Dexie.js for offline-first data storage
- **State Management**: React Query (TanStack Query) for server state
- **Routing**: React Router DOM
- **Internationalization**: Custom i18n with Arabic/English support

### Key Dependencies
- `dexie`: IndexedDB wrapper for data persistence
- `@tanstack/react-query`: Data fetching and caching
- `next-themes`: Theme management (dark/light mode)
- `react-hook-form`: Form handling
- `zod`: Schema validation
- `lucide-react`: Icon library

## Architecture Principles

### 1. Offline-First Design
- All data operations must work offline using IndexedDB
- Implement progressive enhancement for online features
- Use service workers for caching when applicable

### 2. Component-Based Architecture
- Follow atomic design principles (atoms, molecules, organisms)
- Keep components small, focused, and reusable
- Use composition over inheritance

### 3. Data Flow
- Use React Query for server state management
- Local state with useState/useReducer for component state
- IndexedDB as the single source of truth for persistent data

## File Structure Standards

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   ├── Layout/         # Layout components
│   ├── Dashboard/      # Dashboard-specific components
│   ├── Tasks/          # Task management components
│   ├── Projects/       # Project management components
│   ├── Notes/          # Note-taking components
│   ├── AI/             # AI assistant components
│   └── ...
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and configurations
├── pages/              # Page components
├── services/           # Data access layer and API services
├── types/              # TypeScript type definitions
└── utils/              # Helper functions
```

## Coding Standards

### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use proper typing for props, state, and function parameters
- Avoid `any` type - use proper typing or `unknown`

### React Components
- Use functional components with hooks
- Follow PascalCase for component names
- Use descriptive prop interfaces
- Implement proper error boundaries

### Styling
- Use Tailwind CSS classes exclusively
- Follow OKLCH color system defined in theme
- Avoid inline styles
- Use CSS custom properties for dynamic values
- Maintain consistent spacing using Tailwind's spacing scale

### Data Management
- All persistent data must use IndexedDB via Dexie.js
- Implement proper error handling for database operations
- Use React Query for caching and synchronization
- Follow CRUD operation patterns consistently

## Component Guidelines

### UI Components
- Use shadcn/ui components as base
- Extend components through composition, not modification
- Maintain consistent API patterns across similar components
- Include proper accessibility attributes

### Form Handling
- Use react-hook-form for all forms
- Implement Zod schemas for validation
- Provide clear error messages in both languages
- Handle loading and error states appropriately

### Internationalization
- Support Arabic (RTL) and English (LTR) languages
- Use translation keys consistently
- Handle RTL layout properly with Tailwind utilities
- Test all UI components in both languages

## Database Standards

### IndexedDB with Dexie.js
- Define clear database schemas
- Use proper indexing for query performance
- Implement data migration strategies
- Handle database versioning properly

### Data Models
- Define TypeScript interfaces for all entities
- Use consistent naming conventions
- Implement proper relationships between entities
- Include metadata fields (createdAt, updatedAt, etc.)

## Performance Standards

### Code Splitting
- Implement route-based code splitting
- Use React.lazy for heavy components
- Optimize bundle sizes

### Data Loading
- Implement proper loading states
- Use React Query for efficient caching
- Minimize database queries
- Implement pagination for large datasets

## Testing Standards

### Unit Testing
- Test all utility functions
- Test custom hooks
- Test component logic

### Integration Testing
- Test database operations
- Test component interactions
- Test form submissions

## Security Standards

### Data Protection
- Sanitize user inputs
- Validate all data before storage
- Implement proper error handling
- Avoid exposing sensitive information

## Accessibility Standards

### WCAG Compliance
- Maintain WCAG 2.1 AA compliance
- Use semantic HTML elements
- Implement proper ARIA attributes
- Ensure keyboard navigation support
- Test with screen readers

### Color and Contrast
- Use OKLCH color system for consistent contrast
- Avoid color-only information conveyance
- Support high contrast mode

## Git Standards

### Commit Messages
- Use conventional commit format
- Include clear, descriptive messages
- Reference issue numbers when applicable

### Branch Naming
- Use descriptive branch names
- Follow pattern: `feature/description` or `fix/description`

## Documentation Standards

### Code Documentation
- Document complex functions and algorithms
- Use JSDoc for public APIs
- Maintain README files for major features

### Component Documentation
- Document component props and usage
- Include examples for complex components
- Maintain Storybook stories when applicable

## Quality Assurance

### Code Review
- All code must be reviewed before merging
- Check for adherence to these standards
- Verify functionality and performance

### Automated Checks
- Use ESLint for code quality
- Use Prettier for code formatting
- Run TypeScript checks
- Perform accessibility audits

## Deployment Standards

### Build Process
- Optimize for production builds
- Minimize bundle sizes
- Generate source maps for debugging

### Environment Configuration
- Use environment variables for configuration
- Separate development and production settings
- Implement proper error logging

---

This document should be updated as the project evolves and new standards are established.
