# Zenith Flow Theme System Documentation

## Overview

The Zenith Flow application now features a comprehensive dark mode system built on modern OKLCH color space with professional blue-gray palettes. The system provides seamless theme switching, accessibility compliance, and consistent design patterns.

## Key Features

- ✅ **Modern OKLCH Color System**: Professional color space for better color accuracy
- ✅ **Professional Blue-Gray Palettes**: No yellow colors, consistent coordination
- ✅ **Automatic Theme Detection**: Respects system preferences
- ✅ **Smooth Transitions**: Animated theme switching
- ✅ **Accessibility Compliant**: WCAG AA contrast ratios
- ✅ **Centralized Color Management**: Single source of truth for colors

## Theme Architecture

### 1. Theme Provider Setup

The application uses `next-themes` for theme management:

```tsx
// App.tsx
<ThemeProvider
  attribute="class"
  defaultTheme="system"
  enableSystem
  disableTransitionOnChange={true}
>
  {/* App content */}
</ThemeProvider>
```

### 2. CSS Custom Properties

All colors are defined as OKLCH values in CSS custom properties:

```css
:root {
  --background: 0.985 0.002 247.839;
  --foreground: 0.208 0.042 265.755;
  --primary: 0.623 0.214 259.815;
  /* ... */
}

.dark {
  --background: 0.208 0.042 265.755;
  --foreground: 0.985 0.002 247.839;
  --primary: 0.707 0.165 254.624;
  /* ... */
}
```

### 3. Tailwind Configuration

Tailwind CSS is configured to use the OKLCH custom properties:

```ts
// tailwind.config.ts
colors: {
  background: 'oklch(var(--background))',
  foreground: 'oklch(var(--foreground))',
  primary: {
    DEFAULT: 'oklch(var(--primary))',
    foreground: 'oklch(var(--primary-foreground))'
  },
  // ...
}
```

## Usage Guide

### 1. Theme Toggle Component

Use the `ThemeToggle` component for theme switching:

```tsx
import ThemeToggle from '@/components/UI/ThemeToggle';

// Icon-only version (for sidebars)
<ThemeToggle variant="icon-only" />

// Full version (for settings)
<ThemeToggle variant="default" showLabel={true} />

// Compact version
<ThemeToggle variant="compact" />
```

### 2. Theme-Aware Hooks

#### useTheme Hook

Enhanced theme hook with component theme support:

```tsx
import { useTheme } from '@/hooks/useTheme';

const MyComponent = () => {
  const { 
    theme, 
    setTheme, 
    isDark, 
    colors, 
    getThemeStyles,
    toggleTheme 
  } = useTheme();

  // Get theme-aware button styles
  const buttonStyles = getThemeStyles.button('primary', 'md');
  
  return (
    <button 
      onClick={toggleTheme}
      style={buttonStyles}
    >
      Toggle Theme
    </button>
  );
};
```

#### useColors Hook

Enhanced color management with theme awareness:

```tsx
import { useColors } from '@/hooks/useColors';

const MyComponent = () => {
  const { 
    colors, 
    utils: { 
      isDarkMode, 
      getThemeColor, 
      getAdaptiveColor 
    } 
  } = useColors();

  const adaptiveColor = getAdaptiveColor('#ffffff', '#000000');
  
  return <div style={{ color: adaptiveColor }}>Content</div>;
};
```

### 3. Theme Utility Classes

Use pre-built theme-aware utility classes:

```tsx
import { themeClasses } from '@/lib/theme-utils';

// Button with theme support
<button className={themeClasses.button('primary', 'md')}>
  Primary Button
</button>

// Card with theme support
<div className={themeClasses.card('interactive')}>
  Interactive Card
</div>

// Input with theme support
<input className={themeClasses.input('default')} />

// Badge with theme support
<span className={themeClasses.badge('destructive')}>
  Error Badge
</span>
```

### 4. Direct Tailwind Classes

Use Tailwind classes that automatically adapt to themes:

```tsx
// These classes automatically switch between light and dark modes
<div className="bg-background text-foreground">
  <div className="bg-card text-card-foreground border border-border">
    <button className="bg-primary text-primary-foreground hover:bg-primary/90">
      Themed Button
    </button>
  </div>
</div>
```

## Color Palette

### Light Mode
- **Background**: `oklch(0.985 0.002 247.839)` - Very light blue-gray
- **Foreground**: `oklch(0.208 0.042 265.755)` - Dark blue-gray
- **Primary**: `oklch(0.623 0.214 259.815)` - Professional blue
- **Card**: `oklch(1 0 0)` - Pure white

### Dark Mode
- **Background**: `oklch(0.208 0.042 265.755)` - Dark blue-gray
- **Foreground**: `oklch(0.985 0.002 247.839)` - Light blue-gray
- **Primary**: `oklch(0.707 0.165 254.624)` - Bright blue
- **Card**: `oklch(0.279 0.041 260.031)` - Elevated dark blue-gray

## Component Themes

### Button Variants
- `primary`: Main action buttons
- `secondary`: Secondary actions
- `destructive`: Delete/danger actions
- `outline`: Outlined buttons
- `ghost`: Minimal buttons

### Card Variants
- `default`: Standard cards
- `elevated`: Cards with shadow
- `outlined`: Bordered cards
- `interactive`: Hoverable cards
- `muted`: Subdued cards

### Input States
- `default`: Normal state
- `error`: Error state
- `disabled`: Disabled state
- `readonly`: Read-only state

## Best Practices

### 1. Always Use Theme-Aware Colors

❌ **Don't use hardcoded colors:**
```tsx
<div style={{ backgroundColor: '#ffffff', color: '#000000' }}>
```

✅ **Use theme-aware colors:**
```tsx
<div className="bg-background text-foreground">
```

### 2. Use Semantic Color Names

❌ **Don't use specific color names:**
```tsx
<div className="bg-blue-500 text-white">
```

✅ **Use semantic names:**
```tsx
<div className="bg-primary text-primary-foreground">
```

### 3. Test Both Themes

Always test your components in both light and dark modes to ensure proper contrast and readability.

### 4. Use Theme Utilities

Leverage the provided theme utilities for consistent styling:

```tsx
import { cssVars, themeClasses } from '@/lib/theme-utils';

// Get current theme colors
const primaryColor = cssVars.colors.primary();

// Use theme-aware classes
const cardClass = themeClasses.card('interactive');
```

## Accessibility

The theme system ensures WCAG AA compliance:

- **Contrast Ratios**: All color combinations meet 4.5:1 minimum
- **Focus Indicators**: Visible focus rings on interactive elements
- **Color Independence**: Information not conveyed by color alone
- **Reduced Motion**: Respects user's motion preferences

## Migration Guide

### From Old Color System

1. Replace hardcoded colors with theme-aware classes
2. Update component styles to use OKLCH custom properties
3. Test components in both light and dark modes
4. Use the new theme hooks for dynamic styling

### Example Migration

**Before:**
```tsx
<div className="bg-white text-gray-900 border-gray-200">
  <button className="bg-blue-500 text-white hover:bg-blue-600">
    Click me
  </button>
</div>
```

**After:**
```tsx
<div className="bg-card text-card-foreground border-border">
  <button className="bg-primary text-primary-foreground hover:bg-primary/90">
    Click me
  </button>
</div>
```

## Troubleshooting

### Theme Not Switching
- Ensure ThemeProvider wraps your app
- Check that CSS custom properties are defined
- Verify Tailwind configuration includes theme colors

### Colors Not Updating
- Clear browser cache
- Check for CSS specificity issues
- Ensure components use theme-aware classes

### Performance Issues
- Theme transitions are optimized with CSS
- Use `disableTransitionOnChange={true}` in ThemeProvider
- Avoid inline styles that don't respect themes

## Files Updated

The following files have been updated for the new theme system:

- `src/App.tsx` - ThemeProvider setup
- `src/components/UI/ThemeToggle.tsx` - Theme toggle component
- `src/components/Settings/Settings.tsx` - Settings integration
- `src/components/Layout/Sidebar.tsx` - Sidebar theme toggle
- `src/lib/component-themes.ts` - Component theme definitions
- `src/hooks/useTheme.ts` - Enhanced theme hook
- `src/hooks/useColors.ts` - Enhanced color hook
- `src/lib/theme-utils.ts` - Theme utility functions
- `src/index.css` - Theme transitions and utilities
- `tailwind.config.ts` - OKLCH color configuration
