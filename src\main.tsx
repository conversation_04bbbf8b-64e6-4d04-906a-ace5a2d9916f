
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { LanguageProvider } from '@/contexts/LanguageContext'
import './index.css'
import App from './App.tsx'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <BrowserRouter>
      <LanguageProvider>
        <App />
      </LanguageProvider>
    </BrowserRouter>
  </StrictMode>,
)
