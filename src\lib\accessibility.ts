/**
 * Advanced Accessibility Utilities for Zenith Flow
 * أدوات إمكانية الوصول المتقدمة لتطبيق Zenith Flow
 * 
 * Provides comprehensive accessibility features including:
 * - Focus management
 * - Keyboard navigation
 * - ARIA enhancements
 * - Screen reader support
 * - Color contrast validation
 */

import { colors } from './colors';

// Focus management utilities
export class FocusManager {
  private static focusableSelectors = [
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[role="button"]:not([disabled])',
    '[role="link"]:not([disabled])'
  ].join(', ');

  static getFocusableElements(container: HTMLElement): HTMLElement[] {
    return Array.from(container.querySelectorAll(this.focusableSelectors));
  }

  static trapFocus(container: HTMLElement): () => void {
    const focusableElements = this.getFocusableElements(container);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement?.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement?.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }

  static restoreFocus(previousElement: HTMLElement | null) {
    if (previousElement && document.contains(previousElement)) {
      previousElement.focus();
    }
  }
}

// Keyboard navigation utilities
export class KeyboardNavigation {
  static handleArrowNavigation(
    event: KeyboardEvent,
    elements: HTMLElement[],
    currentIndex: number,
    orientation: 'horizontal' | 'vertical' | 'both' = 'both'
  ): number {
    const { key } = event;
    let newIndex = currentIndex;

    switch (key) {
      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          newIndex = currentIndex > 0 ? currentIndex - 1 : elements.length - 1;
        }
        break;
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          newIndex = currentIndex < elements.length - 1 ? currentIndex + 1 : 0;
        }
        break;
      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          newIndex = currentIndex > 0 ? currentIndex - 1 : elements.length - 1;
        }
        break;
      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          newIndex = currentIndex < elements.length - 1 ? currentIndex + 1 : 0;
        }
        break;
      case 'Home':
        event.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        event.preventDefault();
        newIndex = elements.length - 1;
        break;
    }

    if (newIndex !== currentIndex) {
      elements[newIndex]?.focus();
    }

    return newIndex;
  }
}

// ARIA utilities
export class AriaUtils {
  static announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  static setAriaExpanded(element: HTMLElement, expanded: boolean) {
    element.setAttribute('aria-expanded', expanded.toString());
  }

  static setAriaSelected(element: HTMLElement, selected: boolean) {
    element.setAttribute('aria-selected', selected.toString());
  }

  static setAriaPressed(element: HTMLElement, pressed: boolean) {
    element.setAttribute('aria-pressed', pressed.toString());
  }

  static generateId(prefix: string = 'zenith'): string {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Color contrast utilities
export class ColorContrast {
  // Convert OKLCH to RGB for accurate contrast calculations
  static oklchToRgb(l: number, c: number, h: number): [number, number, number] {
    // Simplified conversion - in production, use a proper OKLCH to RGB library
    // This is an approximation for demonstration
    const a = c * Math.cos(h * Math.PI / 180);
    const b = c * Math.sin(h * Math.PI / 180);
    
    // Convert Lab to XYZ (simplified)
    const y = (l + 16) / 116;
    const x = a / 500 + y;
    const z = y - b / 200;
    
    // Convert XYZ to RGB (simplified)
    const r = Math.max(0, Math.min(1, 3.2406 * x - 1.5372 * y - 0.4986 * z));
    const g = Math.max(0, Math.min(1, -0.9689 * x + 1.8758 * y + 0.0415 * z));
    const blue = Math.max(0, Math.min(1, 0.0557 * x - 0.2040 * y + 1.0570 * z));
    
    return [Math.round(r * 255), Math.round(g * 255), Math.round(blue * 255)];
  }

  static getLuminance(rgb: [number, number, number]): number {
    const [r, g, b] = rgb.map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  static getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
    const lum1 = this.getLuminance(color1);
    const lum2 = this.getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  }

  static meetsWCAG(contrastRatio: number, level: 'AA' | 'AAA' = 'AA', size: 'normal' | 'large' = 'normal'): boolean {
    if (level === 'AAA') {
      return size === 'large' ? contrastRatio >= 4.5 : contrastRatio >= 7;
    }
    return size === 'large' ? contrastRatio >= 3 : contrastRatio >= 4.5;
  }
}

// Screen reader utilities
export class ScreenReaderUtils {
  static hideFromScreenReader(element: HTMLElement) {
    element.setAttribute('aria-hidden', 'true');
  }

  static showToScreenReader(element: HTMLElement) {
    element.removeAttribute('aria-hidden');
  }

  static setScreenReaderOnly(element: HTMLElement) {
    element.className += ' sr-only';
  }

  static describedBy(element: HTMLElement, descriptionId: string) {
    const existingIds = element.getAttribute('aria-describedby') || '';
    const ids = existingIds ? `${existingIds} ${descriptionId}` : descriptionId;
    element.setAttribute('aria-describedby', ids);
  }

  static labelledBy(element: HTMLElement, labelId: string) {
    element.setAttribute('aria-labelledby', labelId);
  }
}

// Accessibility validation utilities
export class AccessibilityValidator {
  static validateColorContrast(foreground: string, background: string): {
    ratio: number;
    meetsAA: boolean;
    meetsAAA: boolean;
    recommendations: string[];
  } {
    // This would implement proper color parsing and contrast calculation
    // For now, return a simplified validation
    return {
      ratio: 4.5, // Placeholder
      meetsAA: true,
      meetsAAA: false,
      recommendations: []
    };
  }

  static validateFocusIndicators(element: HTMLElement): boolean {
    const computedStyle = window.getComputedStyle(element, ':focus-visible');
    return computedStyle.outline !== 'none' || computedStyle.boxShadow !== 'none';
  }

  static validateAriaLabels(container: HTMLElement): string[] {
    const issues: string[] = [];
    const interactiveElements = FocusManager.getFocusableElements(container);

    interactiveElements.forEach(element => {
      const hasLabel = element.getAttribute('aria-label') || 
                      element.getAttribute('aria-labelledby') ||
                      (element as HTMLInputElement).labels?.length > 0;
      
      if (!hasLabel && !element.textContent?.trim()) {
        issues.push(`Element ${element.tagName} lacks accessible name`);
      }
    });

    return issues;
  }
}

// Export all utilities
export {
  FocusManager,
  KeyboardNavigation,
  AriaUtils,
  ColorContrast,
  ScreenReaderUtils,
  AccessibilityValidator
};

// Default export for convenience
export default {
  FocusManager,
  KeyboardNavigation,
  AriaUtils,
  ColorContrast,
  ScreenReaderUtils,
  AccessibilityValidator
};
