import React from 'react';
import SettingsPanel from '@/components/Settings/SettingsPanel';
import { useLanguage } from '@/contexts/LanguageContext';

const Settings: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">
          {t('settings.title')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {t('settings.description')}
        </p>
      </div>
      
      <SettingsPanel />
    </div>
  );
};

export default Settings;
