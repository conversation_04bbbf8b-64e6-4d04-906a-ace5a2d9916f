
import React from 'react';
import { Calendar, CheckSquare, FileText, BarChart3, Settings, Plus, Home } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTaskStats, useProjectStats, useNoteStats } from '@/hooks/useData';
import LanguageToggle from '@/components/UI/LanguageToggle';
import ThemeToggle from '@/components/UI/ThemeToggle';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

interface NavItem {
  id: string;
  labelKey: string;
  icon: React.ReactNode;
  count?: number;
}

const Sidebar: React.FC<SidebarProps> = ({ activeSection, onSectionChange }) => {
  const { t, isRTL } = useLanguage();

  // Fetch real data counts
  const { data: taskStats } = useTaskStats();
  const { data: projectStats } = useProjectStats();
  const { data: noteStats } = useNoteStats();

  const navItems: NavItem[] = [
    { id: 'dashboard', labelKey: 'nav.dashboard', icon: <Home className="w-5 h-5" /> },
    {
      id: 'tasks',
      labelKey: 'nav.tasks',
      icon: <CheckSquare className="w-5 h-5" />,
      count: taskStats?.total || 0
    },
    {
      id: 'projects',
      labelKey: 'nav.projects',
      icon: <Calendar className="w-5 h-5" />,
      count: projectStats?.total || 0
    },
    {
      id: 'notes',
      labelKey: 'nav.notes',
      icon: <FileText className="w-5 h-5" />,
      count: noteStats?.total || 0
    },
    { id: 'analytics', labelKey: 'nav.analytics', icon: <BarChart3 className="w-5 h-5" /> },
  ];

  return (
    <div className={cn(
      "w-64 bg-card border border-border h-screen sticky top-0 flex flex-col transition-colors duration-200",
      isRTL ? "border-l" : "border-r"
    )}>
      {/* Logo and Brand */}
      <div className="p-6 border-b border-border">
        <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
          <div className="w-10 h-10 rounded-xl bg-zenith-gradient flex items-center justify-center text-white font-bold text-lg zenith-glow">
            Z
          </div>
          <div className={cn(isRTL && "text-right")}>
            <h1 className="font-bold text-xl zenith-gradient-text">Zenith Flow</h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {isRTL ? 'الإنتاجية السلسة' : 'Effortless Productivity'}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navItems.map((item) => (
          <button
            key={item.id}
            onClick={() => onSectionChange(item.id)}
            className={cn(
              "w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-all duration-200 group",
              activeSection === item.id
                ? "bg-zenith-gradient text-white shadow-zenith"
                : "hover:bg-muted text-foreground hover:shadow-md",
              isRTL && "text-right flex-row-reverse"
            )}
          >
            <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
              <div className={cn(
                "transition-transform duration-200",
                activeSection === item.id ? "scale-110" : "group-hover:scale-105"
              )}>
                {item.icon}
              </div>
              <span className="font-medium">{t(item.labelKey)}</span>
            </div>
            {item.count !== undefined && item.count > 0 && (
              <span className={cn(
                "px-2 py-1 rounded-full text-xs font-semibold min-w-[20px] text-center",
                activeSection === item.id
                  ? "bg-white/20 text-white"
                  : "bg-zenith-sage-100 text-zenith-sage-700 dark:bg-zenith-sage-900 dark:text-zenith-sage-300"
              )}>
                {item.count}
              </span>
            )}
          </button>
        ))}
      </nav>

      {/* Language Toggle & Actions */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-3">
        <div className={cn(
          "flex gap-2",
          isRTL ? "flex-row-reverse" : "flex-row"
        )}>
          <LanguageToggle />
          <ThemeToggle variant="icon-only" className="flex-shrink-0" />
        </div>
        
        <button className="w-full flex items-center gap-3 px-4 py-3 rounded-lg zenith-button text-white font-medium hover:shadow-zenith-lg group">
          <Plus className="w-5 h-5 transition-transform group-hover:rotate-90 duration-200" />
          <span>{t('tasks.newTask')}</span>
        </button>
        
        <button
          onClick={() => onSectionChange('settings')}
          className={cn(
            "w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200",
            activeSection === 'settings'
              ? "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
              : "hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",
            isRTL && "flex-row-reverse"
          )}
        >
          <Settings className="w-5 h-5" />
          <span>{t('nav.settings')}</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
