import { useMemo } from 'react';
import { useTheme } from 'next-themes';
import {
  colors,
  componentColors,
  chartColors,
  gradients,
  getColor,
  getComponentColor,
  type ColorVariant,
  type ColorShade
} from '@/lib/colors';

/**
 * Hook مخصص لإدارة الألوان في التطبيق
 * Custom hook for managing colors throughout the application
 * Enhanced with theme awareness and OKLCH support
 */
export const useColors = () => {
  const { resolvedTheme } = useTheme();
  // دالة للحصول على ألوان الأولوية
  const getPriorityColors = useMemo(() => ({
    high: {
      className: `bg-[${componentColors.priority.high.bg}] text-[${componentColors.priority.high.text}] dark:bg-[${componentColors.priority.high.bgDark}] dark:text-[${componentColors.priority.high.textDark}]`,
      style: {
        backgroundColor: componentColors.priority.high.bg,
        color: componentColors.priority.high.text
      },
      darkStyle: {
        backgroundColor: componentColors.priority.high.bgDark,
        color: componentColors.priority.high.textDark
      }
    },
    medium: {
      className: `bg-[${componentColors.priority.medium.bg}] text-[${componentColors.priority.medium.text}] dark:bg-[${componentColors.priority.medium.bgDark}] dark:text-[${componentColors.priority.medium.textDark}]`,
      style: {
        backgroundColor: componentColors.priority.medium.bg,
        color: componentColors.priority.medium.text
      },
      darkStyle: {
        backgroundColor: componentColors.priority.medium.bgDark,
        color: componentColors.priority.medium.textDark
      }
    },
    low: {
      className: `bg-[${componentColors.priority.low.bg}] text-[${componentColors.priority.low.text}] dark:bg-[${componentColors.priority.low.bgDark}] dark:text-[${componentColors.priority.low.textDark}]`,
      style: {
        backgroundColor: componentColors.priority.low.bg,
        color: componentColors.priority.low.text
      },
      darkStyle: {
        backgroundColor: componentColors.priority.low.bgDark,
        color: componentColors.priority.low.textDark
      }
    }
  }), []);

  // دالة للحصول على ألوان حالة المشاريع
  const getProjectStatusColors = useMemo(() => ({
    active: {
      className: `bg-[${componentColors.projectStatus.active.bg}] text-[${componentColors.projectStatus.active.text}] dark:bg-[${componentColors.projectStatus.active.bgDark}] dark:text-[${componentColors.projectStatus.active.textDark}]`,
      style: {
        backgroundColor: componentColors.projectStatus.active.bg,
        color: componentColors.projectStatus.active.text
      }
    },
    completed: {
      className: `bg-[${componentColors.projectStatus.completed.bg}] text-[${componentColors.projectStatus.completed.text}] dark:bg-[${componentColors.projectStatus.completed.bgDark}] dark:text-[${componentColors.projectStatus.completed.textDark}]`,
      style: {
        backgroundColor: componentColors.projectStatus.completed.bg,
        color: componentColors.projectStatus.completed.text
      }
    },
    pending: {
      className: `bg-[${componentColors.projectStatus.pending.bg}] text-[${componentColors.projectStatus.pending.text}] dark:bg-[${componentColors.projectStatus.pending.bgDark}] dark:text-[${componentColors.projectStatus.pending.textDark}]`,
      style: {
        backgroundColor: componentColors.projectStatus.pending.bg,
        color: componentColors.projectStatus.pending.text
      }
    }
  }), []);

  // دالة للحصول على ألوان البطاقات الإحصائية
  const getStatsCardColors = useMemo(() => ({
    primary: {
      className: `bg-[${componentColors.statsCards.primary.bg}] border-[${componentColors.statsCards.primary.border}] text-[${componentColors.statsCards.primary.icon}] dark:bg-[${componentColors.statsCards.primary.bgDark}] dark:border-[${componentColors.statsCards.primary.borderDark}] dark:text-[${componentColors.statsCards.primary.iconDark}]`,
      style: {
        backgroundColor: componentColors.statsCards.primary.bg,
        borderColor: componentColors.statsCards.primary.border,
        color: componentColors.statsCards.primary.icon
      }
    },
    secondary: {
      className: `bg-[${componentColors.statsCards.secondary.bg}] border-[${componentColors.statsCards.secondary.border}] text-[${componentColors.statsCards.secondary.icon}] dark:bg-[${componentColors.statsCards.secondary.bgDark}] dark:border-[${componentColors.statsCards.secondary.borderDark}] dark:text-[${componentColors.statsCards.secondary.iconDark}]`,
      style: {
        backgroundColor: componentColors.statsCards.secondary.bg,
        borderColor: componentColors.statsCards.secondary.border,
        color: componentColors.statsCards.secondary.icon
      }
    },
    success: {
      className: `bg-[${componentColors.statsCards.success.bg}] border-[${componentColors.statsCards.success.border}] text-[${componentColors.statsCards.success.icon}] dark:bg-[${componentColors.statsCards.success.bgDark}] dark:border-[${componentColors.statsCards.success.borderDark}] dark:text-[${componentColors.statsCards.success.iconDark}]`,
      style: {
        backgroundColor: componentColors.statsCards.success.bg,
        borderColor: componentColors.statsCards.success.border,
        color: componentColors.statsCards.success.icon
      }
    },
    neutral: {
      className: `bg-[${componentColors.statsCards.neutral.bg}] border-[${componentColors.statsCards.neutral.border}] text-[${componentColors.statsCards.neutral.icon}] dark:bg-[${componentColors.statsCards.neutral.bgDark}] dark:border-[${componentColors.statsCards.neutral.borderDark}] dark:text-[${componentColors.statsCards.neutral.iconDark}]`,
      style: {
        backgroundColor: componentColors.statsCards.neutral.bg,
        borderColor: componentColors.statsCards.neutral.border,
        color: componentColors.statsCards.neutral.icon
      }
    }
  }), []);

  return {
    // الألوان الأساسية
    colors,
    getColor,
    
    // ألوان المكونات
    componentColors,
    getComponentColor,
    
    // ألوان محددة مسبقاً
    priority: getPriorityColors,
    projectStatus: getProjectStatusColors,
    statsCards: getStatsCardColors,
    
    // ألوان الرسوم البيانية
    chart: chartColors,
    
    // التدرجات
    gradients,
    
    // دوال مساعدة محسنة مع ميزات إمكانية الوصول
    utils: {
      // دالة للحصول على لون بناءً على الحالة
      getStatusColor: (status: 'success' | 'warning' | 'error' | 'info') => {
        switch (status) {
          case 'success': return colors.success[500];
          case 'warning': return colors.warning[500];
          case 'error': return colors.error[500];
          case 'info': return colors.primary[500];
          default: return colors.neutral[500];
        }
      },

      // دالة للحصول على لون مع شفافية (OKLCH compatible)
      withOpacity: (color: string, opacity: number) => `${color} / ${opacity}`,

      // دالة محسنة للحصول على لون متباين مع فحص إمكانية الوصول
      getAccessibleTextColor: (backgroundColor: string): string => {
        // Enhanced logic for OKLCH colors
        if (backgroundColor.includes('oklch')) {
          // Extract lightness value from OKLCH string
          const lightnessMatch = backgroundColor.match(/oklch\(([0-9.]+)/);
          if (lightnessMatch) {
            const lightness = parseFloat(lightnessMatch[1]);
            return lightness > 0.6 ? colors.neutral[900] : colors.neutral[50];
          }
        }

        // Fallback for other color formats
        return backgroundColor.includes('900') || backgroundColor.includes('800') || backgroundColor.includes('700')
          ? colors.neutral[50]
          : colors.neutral[900];
      },

      // فحص إمكانية الوصول للألوان
      isAccessible: (foreground: string, background: string): boolean => {
        // Simplified accessibility check - in production, use proper contrast calculation
        const darkColors = ['700', '800', '900'];
        const lightColors = ['50', '100', '200', '300'];

        const isForegroundDark = darkColors.some(shade => foreground.includes(shade));
        const isBackgroundLight = lightColors.some(shade => background.includes(shade));
        const isForegroundLight = lightColors.some(shade => foreground.includes(shade));
        const isBackgroundDark = darkColors.some(shade => background.includes(shade));

        return (isForegroundDark && isBackgroundLight) || (isForegroundLight && isBackgroundDark);
      },

      // الحصول على مجموعات ألوان دلالية متاحة
      getSemanticColorSet: (type: 'success' | 'warning' | 'error' | 'info') => ({
        background: colors[type === 'info' ? 'primary' : type][50],
        border: colors[type === 'info' ? 'primary' : type][200],
        text: colors[type === 'info' ? 'primary' : type][700],
        icon: colors[type === 'info' ? 'primary' : type][500],
        hover: colors[type === 'info' ? 'primary' : type][100]
      }),

      // تحويل الألوان إلى متغيرات CSS
      toCSSVariable: (colorPath: string): string => {
        return `oklch(var(--${colorPath.replace('.', '-')}))`;
      },

      // فحص ما إذا كان اللون فاتحاً أم داكناً
      isLightColor: (color: string): boolean => {
        if (color.includes('oklch')) {
          const lightnessMatch = color.match(/oklch\(([0-9.]+)/);
          if (lightnessMatch) {
            return parseFloat(lightnessMatch[1]) > 0.6;
          }
        }
        return ['50', '100', '200', '300', '400'].some(shade => color.includes(shade));
      },

      // Theme-aware color utilities
      getCurrentTheme: () => resolvedTheme,
      isDarkMode: () => resolvedTheme === 'dark',
      isLightMode: () => resolvedTheme === 'light',

      // Get CSS custom property values
      getCSSVar: (property: string): string => {
        if (typeof window !== 'undefined') {
          return getComputedStyle(document.documentElement).getPropertyValue(property);
        }
        return '';
      },

      // Get theme-aware OKLCH colors
      getThemeColor: (cssVar: string): string => {
        if (typeof window !== 'undefined') {
          const value = getComputedStyle(document.documentElement).getPropertyValue(cssVar);
          return value ? `oklch(${value})` : '';
        }
        return '';
      },

      // Enhanced theme-aware color selection
      getAdaptiveColor: (lightColor: string, darkColor: string): string => {
        return resolvedTheme === 'dark' ? darkColor : lightColor;
      }
    }
  };
};

export default useColors;
