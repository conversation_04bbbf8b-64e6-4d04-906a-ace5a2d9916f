
import React from 'react';
import { Plus, Calendar, FileText, BarChart3 } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useTasks } from '@/hooks/useData';

const QuickActions: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { data: recentTasks } = useTasks({}, { page: 1, limit: 3, sortBy: 'updatedAt', sortOrder: 'desc' });

  const actions = [
    {
      icon: Plus,
      title: t('tasks.newTask'),
      description: isRTL ? 'أنشئ مهمة جديدة بسرعة' : 'Create a new task quickly',
      color: 'from-sky-500 to-sky-600',
    },
    {
      icon: Calendar,
      title: t('projects.newProject'),
      description: isRTL ? 'ابدأ مشروعاً جديداً' : 'Start a new project',
      color: 'from-slate-500 to-slate-600',
    },
    {
      icon: FileText,
      title: t('notes.newNote'),
      description: isRTL ? 'اكتب ملاحظة سريعة' : 'Write a quick note',
      color: 'from-lavender-500 to-lavender-600',
    },
    {
      icon: BarChart3,
      title: t('analytics.title'),
      description: isRTL ? 'عرض إحصائيات الإنتاجية' : 'View productivity stats',
      color: 'from-blue-500 to-blue-600',
    },
  ];

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">{isRTL ? 'إجراءات سريعة' : 'Quick Actions'}</h2>
      
      <div className="space-y-3">
        {actions.map((action, index) => (
          <button
            key={index}
            className={cn(
              "w-full p-4 rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:shadow-lg hover:shadow-gray-200/50 dark:hover:shadow-gray-900/50 transition-all duration-200 group",
              isRTL && "text-right"
            )}
          >
            <div className={cn(
              "flex items-center gap-4",
              isRTL && "flex-row-reverse"
            )}>
              <div className={cn(
                "p-3 rounded-lg bg-gradient-to-r text-white group-hover:scale-110 transition-transform duration-200",
                action.color
              )}>
                <action.icon className="w-5 h-5" />
              </div>
              <div className={cn("flex-1", isRTL && "text-right")}>
                <h3 className="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {action.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {action.description}
                </p>
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Recent Activity */}
      {recentTasks && recentTasks.data.length > 0 && (
        <div className="mt-8 p-4 bg-gradient-to-br from-blue-50 to-slate-50 dark:from-blue-900/20 dark:to-slate-900/20 rounded-xl border border-blue-200 dark:border-blue-700">
          <h3 className={cn(
            "font-semibold mb-3 text-blue-700 dark:text-blue-300",
            isRTL && "text-right"
          )}>
            {isRTL ? 'المهام الأخيرة' : 'Recent Tasks'}
          </h3>
          <div className="space-y-2">
            {recentTasks.data.map((task) => (
              <div
                key={task.id}
                className={cn(
                  "flex items-center justify-between text-sm",
                  isRTL && "flex-row-reverse"
                )}
              >
                <span className="text-muted-foreground truncate flex-1">
                  {task.title}
                </span>
                <span className={cn(
                  "text-xs px-2 py-1 rounded-full ml-2",
                  task.status === 'completed' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                  task.status === 'inProgress' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                  'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'
                )}>
                  {task.status === 'completed' ? (isRTL ? 'مكتملة' : 'Done') :
                   task.status === 'inProgress' ? (isRTL ? 'قيد التنفيذ' : 'In Progress') :
                   (isRTL ? 'جديدة' : 'New')}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuickActions;
