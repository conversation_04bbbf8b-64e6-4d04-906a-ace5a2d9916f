import { db } from '@/lib/database';
import { QueryClient } from '@tanstack/react-query';

export interface DataResetOptions {
  clearCache?: boolean;
  clearLocalStorage?: boolean;
  clearSessionStorage?: boolean;
  preserveSettings?: boolean;
}

export interface DataResetProgress {
  step: string;
  progress: number;
  total: number;
  message: string;
}

export interface DataResetResult {
  success: boolean;
  message: string;
  error?: string;
  clearedTables: string[];
  preservedData?: Record<string, unknown>;
}

export class DataResetService {
  private queryClient: QueryClient | null = null;

  constructor(queryClient?: QueryClient) {
    this.queryClient = queryClient || null;
  }

  /**
   * Performs a complete data reset of the application
   */
  async resetAllData(
    options: DataResetOptions = {},
    onProgress?: (progress: DataResetProgress) => void
  ): Promise<DataResetResult> {
    const {
      clearCache = true,
      clearLocalStorage = true,
      clearSessionStorage = true,
      preserveSettings = false
    } = options;

    const result: DataResetResult = {
      success: false,
      message: '',
      clearedTables: [],
      preservedData: {}
    };

    try {
      let currentStep = 0;
      const totalSteps = 6;

      // Step 1: Preserve settings if requested
      onProgress?.({
        step: 'preserve-settings',
        progress: ++currentStep,
        total: totalSteps,
        message: 'Preserving user settings...'
      });

      let preservedSettings = {};
      if (preserveSettings) {
        preservedSettings = await this.preserveUserSettings();
        result.preservedData = preservedSettings;
      }

      // Step 2: Clear React Query cache
      if (clearCache && this.queryClient) {
        onProgress?.({
          step: 'clear-cache',
          progress: ++currentStep,
          total: totalSteps,
          message: 'Clearing application cache...'
        });

        this.queryClient.clear();
      } else {
        currentStep++;
      }

      // Step 3: Clear IndexedDB data
      onProgress?.({
        step: 'clear-database',
        progress: ++currentStep,
        total: totalSteps,
        message: 'Clearing database...'
      });

      const clearedTables = await this.clearDatabaseData();
      result.clearedTables = clearedTables;

      // Step 4: Clear localStorage
      if (clearLocalStorage) {
        onProgress?.({
          step: 'clear-localstorage',
          progress: ++currentStep,
          total: totalSteps,
          message: 'Clearing local storage...'
        });

        await this.clearLocalStorage(preserveSettings);
      } else {
        currentStep++;
      }

      // Step 5: Clear sessionStorage
      if (clearSessionStorage) {
        onProgress?.({
          step: 'clear-sessionstorage',
          progress: ++currentStep,
          total: totalSteps,
          message: 'Clearing session storage...'
        });

        await this.clearSessionStorage();
      } else {
        currentStep++;
      }

      // Step 6: Restore preserved settings
      if (preserveSettings && Object.keys(preservedSettings).length > 0) {
        onProgress?.({
          step: 'restore-settings',
          progress: ++currentStep,
          total: totalSteps,
          message: 'Restoring user settings...'
        });

        await this.restoreUserSettings(preservedSettings);
      } else {
        currentStep++;
      }

      result.success = true;
      result.message = 'All data has been successfully reset';

      return result;
    } catch (error) {
      result.success = false;
      result.message = 'Failed to reset data';
      result.error = error instanceof Error ? error.message : 'Unknown error occurred';
      
      console.error('Data reset failed:', error);
      return result;
    }
  }

  /**
   * Clears all data from IndexedDB tables
   */
  private async clearDatabaseData(): Promise<string[]> {
    const clearedTables: string[] = [];

    try {
      // Get all table names
      const tableNames = [
        'tasks',
        'projects', 
        'notes',
        'users',
        'tags',
        'attachments',
        'timeEntries',
        'comments',
        'reminders',
        'searchIndex',
        'productivityMetrics',
        'goals',
        'activityLogs'
      ];

      // Clear each table
      await db.transaction('rw', db.tables, async () => {
        for (const tableName of tableNames) {
          try {
            const table = db[tableName as keyof typeof db];
            if (table) {
              await table.clear();
              clearedTables.push(tableName);
            }
          } catch (error) {
            console.warn(`Failed to clear table ${tableName}:`, error);
          }
        }
      });

      return clearedTables;
    } catch (error) {
      console.error('Failed to clear database data:', error);
      throw error;
    }
  }

  /**
   * Preserves user settings before reset
   */
  private async preserveUserSettings(): Promise<Record<string, unknown>> {
    try {
      const settings: Record<string, unknown> = {};

      // Preserve theme setting
      const theme = localStorage.getItem('theme');
      if (theme) {
        settings.theme = theme;
      }

      // Preserve language setting
      const language = localStorage.getItem('language');
      if (language) {
        settings.language = language;
      }

      // Preserve any other user preferences
      const userPrefs = localStorage.getItem('userPreferences');
      if (userPrefs) {
        try {
          settings.userPreferences = JSON.parse(userPrefs);
        } catch (error) {
          console.warn('Failed to parse user preferences:', error);
        }
      }

      return settings;
    } catch (error) {
      console.error('Failed to preserve user settings:', error);
      return {};
    }
  }

  /**
   * Restores user settings after reset
   */
  private async restoreUserSettings(settings: Record<string, unknown>): Promise<void> {
    try {
      // Restore theme
      if (settings.theme) {
        localStorage.setItem('theme', settings.theme as string);
      }

      // Restore language
      if (settings.language) {
        localStorage.setItem('language', settings.language as string);
      }

      // Restore user preferences
      if (settings.userPreferences) {
        localStorage.setItem('userPreferences', JSON.stringify(settings.userPreferences));
      }
    } catch (error) {
      console.error('Failed to restore user settings:', error);
    }
  }

  /**
   * Clears localStorage while optionally preserving settings
   */
  private async clearLocalStorage(preserveSettings: boolean = false): Promise<void> {
    try {
      if (preserveSettings) {
        // Clear all except preserved keys
        const keysToPreserve = ['theme', 'language', 'userPreferences'];
        const allKeys = Object.keys(localStorage);
        
        for (const key of allKeys) {
          if (!keysToPreserve.includes(key)) {
            localStorage.removeItem(key);
          }
        }
      } else {
        localStorage.clear();
      }
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  }

  /**
   * Clears sessionStorage
   */
  private async clearSessionStorage(): Promise<void> {
    try {
      sessionStorage.clear();
    } catch (error) {
      console.error('Failed to clear sessionStorage:', error);
    }
  }

  /**
   * Gets current data statistics
   */
  async getDataStatistics(): Promise<Record<string, number>> {
    try {
      const stats: Record<string, number> = {};

      stats.tasks = await db.tasks.count();
      stats.projects = await db.projects.count();
      stats.notes = await db.notes.count();
      stats.users = await db.users.count();
      stats.tags = await db.tags.count();
      stats.attachments = await db.attachments.count();
      stats.timeEntries = await db.timeEntries.count();
      stats.comments = await db.comments.count();
      stats.reminders = await db.reminders.count();
      stats.productivityMetrics = await db.productivityMetrics.count();
      stats.goals = await db.goals.count();
      stats.activityLogs = await db.activityLogs.count();

      return stats;
    } catch (error) {
      console.error('Failed to get data statistics:', error);
      return {};
    }
  }
}

// Export singleton instance
export const dataResetService = new DataResetService();
