
import React, { useMemo } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON>Chart, Pie, Cell } from 'recharts';
import { TrendingUp, Clock, CheckSquare, Calendar } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { useTaskStats, useProjectStats, useNoteStats, useTasks } from '@/hooks/useData';

const AnalyticsDashboard: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { chart } = useColors();

  // Fetch real data
  const { data: taskStats } = useTaskStats();
  const { data: projectStats } = useProjectStats();
  const { data: noteStats } = useNoteStats();
  const { data: tasksResult } = useTasks({}, { page: 1, limit: 1000 });

  // Generate real weekly data from tasks
  const weeklyData = useMemo(() => {
    const today = new Date();
    const last7Days = [];

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      const dayName = isRTL ?
        ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][date.getDay()] :
        ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][date.getDay()];

      // Count tasks completed on this day
      const tasksCompletedOnDay = tasksResult?.data?.filter(task => {
        if (task.status !== 'completed' || !task.completedAt) return false;
        const completedDate = new Date(task.completedAt);
        return completedDate.toDateString() === date.toDateString();
      }).length || 0;

      last7Days.push({
        name: dayName,
        tasks: tasksCompletedOnDay,
        hours: tasksCompletedOnDay * 2, // Estimate 2 hours per task
      });
    }

    return last7Days;
  }, [tasksResult?.data, isRTL]);

  // Generate real productivity data
  const productivityData = useMemo(() => {
    if (!taskStats || !projectStats || !noteStats) return [];

    const total = taskStats.total + projectStats.total + noteStats.total;
    if (total === 0) return [];

    return [
      {
        name: isRTL ? 'مهام' : 'Tasks',
        value: Math.round((taskStats.total / total) * 100),
        color: chart.primary
      },
      {
        name: isRTL ? 'مشاريع' : 'Projects',
        value: Math.round((projectStats.total / total) * 100),
        color: chart.secondary
      },
      {
        name: isRTL ? 'ملاحظات' : 'Notes',
        value: Math.round((noteStats.total / total) * 100),
        color: chart.success
      },
    ];
  }, [taskStats, projectStats, noteStats, chart, isRTL]);

  // Generate real stats cards
  const statsCards = useMemo(() => {
    if (!taskStats || !projectStats) return [];

    const productivityScore = taskStats.total > 0 ?
      Math.round((taskStats.completed / taskStats.total) * 100) : 0;

    return [
      {
        title: t('analytics.tasksCompleted'),
        value: taskStats.completed.toString(),
        subtitle: t('analytics.total'),
        icon: CheckSquare,
        trend: `${taskStats.total - taskStats.completed} ${isRTL ? 'متبقية' : 'remaining'}`,
        color: 'from-green-500 to-green-600',
      },
      {
        title: t('analytics.projectsActive'),
        value: projectStats.active.toString(),
        subtitle: t('analytics.active'),
        icon: Calendar,
        trend: `${projectStats.total} ${isRTL ? 'إجمالي' : 'total'}`,
        color: 'from-purple-500 to-purple-600',
      },
      {
        title: t('analytics.productivity'),
        value: `${productivityScore}%`,
        subtitle: t('analytics.performance'),
        icon: TrendingUp,
        trend: taskStats.completed > 0 ? (isRTL ? 'جيد' : 'Good') : (isRTL ? 'ابدأ العمل' : 'Start working'),
        color: 'from-slate-500 to-slate-600',
      },
    ];
  }, [taskStats, projectStats, t, isRTL]);

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(isRTL && "text-right")}>
        <h1 className="text-3xl font-bold">{t('analytics.title')}</h1>
        <p className="text-muted-foreground mt-2">{t('analytics.subtitle')}</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <div
            key={index}
            className="bg-card border border-border rounded-xl p-6 hover:shadow-card-hover transition-all duration-200"
          >
            <div className={cn(
              "flex items-center justify-between mb-4",
              isRTL && "flex-row-reverse"
            )}>
              <div className={cn(
                `p-3 rounded-lg bg-gradient-to-r ${stat.color} text-white`
              )}>
                <stat.icon className="w-6 h-6" />
              </div>
              <span className="text-xs px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-full font-medium">
                {stat.trend}
              </span>
            </div>
            
            <div className={cn(isRTL && "text-right")}>
              <h3 className="text-2xl font-bold text-foreground mb-1">{stat.value}</h3>
              <p className="text-sm font-medium text-muted-foreground mb-1">{stat.title}</p>
              <p className="text-xs text-muted-foreground">{stat.subtitle}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Performance */}
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 transition-colors duration-200">
          <h2 className={cn(
            "text-xl font-bold mb-6",
            isRTL && "text-right"
          )}>
            {t('analytics.performance')} - {t('analytics.thisWeek')}
          </h2>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="name" 
                  tick={{ fontSize: 12 }}
                  className="text-gray-600 dark:text-gray-400"
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  className="text-gray-600 dark:text-gray-400"
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                  }}
                />
                <Bar
                  dataKey="tasks"
                  fill={chart.primary}
                  radius={[4, 4, 0, 0]}
                  name={isRTL ? 'المهام' : 'Tasks'}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Productivity Distribution */}
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 transition-colors duration-200">
          <h2 className={cn(
            "text-xl font-bold mb-6",
            isRTL && "text-right"
          )}>
            {t('analytics.productivity')} - {t('analytics.overview')}
          </h2>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={productivityData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {productivityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          
          {/* Legend */}
          <div className="flex justify-center gap-4 mt-4">
            {productivityData.map((item, index) => (
              <div key={index} className={cn(
                "flex items-center gap-2",
                isRTL && "flex-row-reverse"
              )}>
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-muted-foreground">{item.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Trends */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h2 className={cn(
          "text-xl font-bold mb-6",
          isRTL && "text-right"
        )}>
          {t('analytics.trends')} - {t('analytics.thisMonth')}
        </h2>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={weeklyData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: 12 }}
                className="text-muted-foreground"
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                className="text-muted-foreground"
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '8px',
                }}
              />
              <Line
                type="monotone"
                dataKey="tasks"
                stroke={chart.primary}
                strokeWidth={3}
                dot={{ fill: chart.primary, strokeWidth: 2, r: 6 }}
                name={isRTL ? 'المهام' : 'Tasks'}
              />
              <Line
                type="monotone"
                dataKey="hours"
                stroke={chart.secondary}
                strokeWidth={3}
                dot={{ fill: chart.secondary, strokeWidth: 2, r: 6 }}
                name={isRTL ? 'الساعات' : 'Hours'}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
