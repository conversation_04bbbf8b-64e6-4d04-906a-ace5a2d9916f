
import React, { useMemo } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useLanguage } from '@/contexts/LanguageContext';
import { useColors } from '@/hooks/useColors';
import { useTasks } from '@/hooks/useData';

const ProductivityChart: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { chart } = useColors();

  // Fetch tasks for the last 7 days
  const { data: tasksResult } = useTasks({}, { page: 1, limit: 1000 });

  // Generate chart data for the last 7 days
  const data = useMemo(() => {
    const today = new Date();
    const last7Days = [];

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      const dayName = isRTL ?
        ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][date.getDay()] :
        ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][date.getDay()];

      // Count tasks completed on this day
      const tasksCompletedOnDay = tasksResult?.data?.filter(task => {
        if (task.status !== 'completed' || !task.completedAt) return false;
        const completedDate = new Date(task.completedAt);
        return completedDate.toDateString() === date.toDateString();
      }).length || 0;

      // Count tasks created on this day
      const tasksCreatedOnDay = tasksResult?.data?.filter(task => {
        const createdDate = new Date(task.createdAt);
        return createdDate.toDateString() === date.toDateString();
      }).length || 0;

      last7Days.push({
        name: dayName,
        completed: tasksCompletedOnDay,
        created: tasksCreatedOnDay,
        date: date.toISOString().split('T')[0]
      });
    }

    return last7Days;
  }, [tasksResult?.data, isRTL]);

  return (
    <div className="bg-card border border-border rounded-xl p-6 transition-colors duration-200">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-2">{t('analytics.productivity')}</h2>
        <p className="text-muted-foreground text-sm">{t('analytics.thisWeek')}</p>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <Tooltip
              contentStyle={{
                backgroundColor: 'hsl(var(--card))',
                border: '1px solid hsl(var(--border))',
                borderRadius: '8px',
                color: 'hsl(var(--foreground))'
              }}
              labelStyle={{ color: 'hsl(var(--foreground))' }}
            />
            <Bar
              dataKey="completed"
              fill={chart.primary}
              radius={[4, 4, 0, 0]}
              name={t('tasks.completed')}
            />
            <Bar
              dataKey="created"
              fill={chart.secondary}
              radius={[4, 4, 0, 0]}
              name={t('tasks.created')}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ProductivityChart;
