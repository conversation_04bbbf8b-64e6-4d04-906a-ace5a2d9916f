import React from 'react';
import TasksList from '@/components/Tasks/TasksList';
import { useLanguage } from '@/contexts/LanguageContext';

const Tasks: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">
          {t('tasks.title')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {t('tasks.description')}
        </p>
      </div>
      
      <TasksList />
    </div>
  );
};

export default Tasks;
