import { db } from '@/lib/database';
import type { Note, NoteFilter, PaginationOptions, PaginatedResult } from '@/types/database';
import { v4 as uuidv4 } from 'uuid';

export class NoteService {
  // Create a new note
  async createNote(noteData: Omit<Note, 'id' | 'createdAt' | 'updatedAt' | 'wordCount'>): Promise<Note> {
    try {
      const wordCount = this.calculateWordCount(noteData.content);
      
      const note: Note = {
        ...noteData,
        id: uuidv4(),
        wordCount,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.notes.add(note);
      
      // Update search index
      await db.updateSearchIndex(
        'note',
        note.id,
        note.title,
        note.content,
        note.tags
      );

      // Log activity
      await db.logActivity(
        'current-user',
        'create',
        'note',
        note.id,
        { title: note.title, wordCount: note.wordCount }
      );

      return note;
    } catch (error) {
      console.error('Error creating note:', error);
      throw new Error('Failed to create note');
    }
  }

  // Get note by ID
  async getNoteById(id: string): Promise<Note | undefined> {
    try {
      const note = await db.notes.get(id);
      
      // Update last viewed timestamp
      if (note) {
        await db.notes.update(id, { lastViewedAt: new Date() });
      }
      
      return note;
    } catch (error) {
      console.error('Error getting note:', error);
      throw new Error('Failed to get note');
    }
  }

  // Get all notes with optional filtering and pagination
  async getNotes(
    filter: NoteFilter = {},
    pagination: PaginationOptions = { page: 1, limit: 50 }
  ): Promise<PaginatedResult<Note>> {
    try {
      // Start with all notes
      let collection = db.notes.toCollection();

      // Apply filters

      if (filter.isPinned !== undefined) {
        collection = collection.filter(note => note.isPinned === filter.isPinned);
      }

      if (filter.isArchived !== undefined) {
        collection = collection.filter(note => note.isArchived === filter.isArchived);
      }

      if (filter.projectId) {
        collection = collection.filter(note => note.projectId === filter.projectId);
      }

      if (filter.taskId) {
        collection = collection.filter(note => note.taskId === filter.taskId);
      }

      if (filter.tags && filter.tags.length > 0) {
        collection = collection.filter(note =>
          filter.tags!.some(tag => note.tags.includes(tag))
        );
      }

      if (filter.createdDateFrom) {
        collection = collection.filter(note =>
          note.createdAt >= filter.createdDateFrom!
        );
      }

      if (filter.createdDateTo) {
        collection = collection.filter(note =>
          note.createdAt <= filter.createdDateTo!
        );
      }

      // Apply sorting
      const sortBy = pagination.sortBy || 'updatedAt';
      const sortOrder = pagination.sortOrder || 'desc';

      if (sortOrder === 'desc') {
        collection = collection.reverse().sortBy(sortBy);
      } else {
        collection = collection.sortBy(sortBy);
      }

      // Get all filtered and sorted data
      const allData = await collection;
      const total = allData.length;

      // Apply pagination
      const offset = (pagination.page - 1) * pagination.limit;
      const data = allData.slice(offset, offset + pagination.limit);

      const totalPages = Math.ceil(total / pagination.limit);

      return {
        data,
        total,
        page: pagination.page,
        limit: pagination.limit,
        totalPages,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1
      };
    } catch (error) {
      console.error('Error getting notes:', error);
      throw new Error('Failed to get notes');
    }
  }

  // Update note
  async updateNote(id: string, updates: Partial<Omit<Note, 'id' | 'createdAt'>>): Promise<Note> {
    try {
      const existingNote = await db.notes.get(id);
      if (!existingNote) {
        throw new Error('Note not found');
      }

      // Recalculate word count if content changed
      const wordCount = updates.content !== undefined 
        ? this.calculateWordCount(updates.content)
        : existingNote.wordCount;

      const updatedNote = {
        ...existingNote,
        ...updates,
        wordCount,
        updatedAt: new Date()
      };

      await db.notes.update(id, updatedNote);

      // Update search index if title, content, or tags changed
      if (updates.title || updates.content || updates.tags) {
        await db.updateSearchIndex(
          'note',
          id,
          updatedNote.title,
          updatedNote.content,
          updatedNote.tags
        );
      }

      // Log activity
      await db.logActivity(
        'current-user',
        'update',
        'note',
        id,
        { changes: updates }
      );

      return updatedNote;
    } catch (error) {
      console.error('Error updating note:', error);
      throw new Error('Failed to update note');
    }
  }

  // Delete note
  async deleteNote(id: string): Promise<void> {
    try {
      const note = await db.notes.get(id);
      if (!note) {
        throw new Error('Note not found');
      }

      await db.transaction('rw', [db.notes, db.searchIndex, db.comments], async () => {
        // Delete note
        await db.notes.delete(id);
        
        // Delete from search index
        await db.searchIndex.where('entityId').equals(id).delete();
        
        // Delete related comments
        await db.comments.where('entityId').equals(id).delete();
      });

      // Log activity
      await db.logActivity(
        'current-user',
        'delete',
        'note',
        id,
        { title: note.title }
      );
    } catch (error) {
      console.error('Error deleting note:', error);
      throw new Error('Failed to delete note');
    }
  }

  // Pin/unpin note
  async togglePinNote(id: string): Promise<Note> {
    try {
      const note = await db.notes.get(id);
      if (!note) {
        throw new Error('Note not found');
      }

      return await this.updateNote(id, { isPinned: !note.isPinned });
    } catch (error) {
      console.error('Error toggling pin note:', error);
      throw new Error('Failed to toggle pin note');
    }
  }

  // Archive/unarchive note
  async toggleArchiveNote(id: string): Promise<Note> {
    try {
      const note = await db.notes.get(id);
      if (!note) {
        throw new Error('Note not found');
      }

      return await this.updateNote(id, { isArchived: !note.isArchived });
    } catch (error) {
      console.error('Error toggling archive note:', error);
      throw new Error('Failed to toggle archive note');
    }
  }

  // Get pinned notes
  async getPinnedNotes(): Promise<Note[]> {
    try {
      return await db.notes
        .where('isPinned')
        .equals(1)
        .and(note => !note.isArchived)
        .sortBy('updatedAt');
    } catch (error) {
      console.error('Error getting pinned notes:', error);
      throw new Error('Failed to get pinned notes');
    }
  }

  // Get recent notes
  async getRecentNotes(limit: number = 10): Promise<Note[]> {
    try {
      return await db.notes
        .where('isArchived')
        .equals(0)
        .reverse()
        .sortBy('updatedAt')
        .then(notes => notes.slice(0, limit));
    } catch (error) {
      console.error('Error getting recent notes:', error);
      throw new Error('Failed to get recent notes');
    }
  }

  // Get notes by project
  async getNotesByProject(projectId: string): Promise<Note[]> {
    try {
      return await db.notes
        .where('projectId')
        .equals(projectId)
        .and(note => !note.isArchived)
        .sortBy('updatedAt');
    } catch (error) {
      console.error('Error getting notes by project:', error);
      throw new Error('Failed to get notes by project');
    }
  }

  // Get notes by task
  async getNotesByTask(taskId: string): Promise<Note[]> {
    try {
      return await db.notes
        .where('taskId')
        .equals(taskId)
        .and(note => !note.isArchived)
        .sortBy('updatedAt');
    } catch (error) {
      console.error('Error getting notes by task:', error);
      throw new Error('Failed to get notes by task');
    }
  }

  // Search notes
  async searchNotes(query: string): Promise<Note[]> {
    try {
      const searchResults = await db.searchEntities(query, ['note']);
      const noteIds = searchResults.map(result => result.entityId);
      
      if (noteIds.length === 0) {
        return [];
      }

      return await db.notes.where('id').anyOf(noteIds).toArray();
    } catch (error) {
      console.error('Error searching notes:', error);
      throw new Error('Failed to search notes');
    }
  }

  // Get note statistics
  async getNoteStats(): Promise<{
    total: number;
    pinned: number;
    archived: number;
    totalWords: number;
    averageWords: number;
  }> {
    try {
      const allNotes = await db.notes.toArray();
      const total = allNotes.length;
      const pinned = allNotes.filter(note => note.isPinned).length;
      const archived = allNotes.filter(note => note.isArchived).length;
      const totalWords = allNotes.reduce((sum, note) => sum + note.wordCount, 0);
      const averageWords = total > 0 ? Math.round(totalWords / total) : 0;

      return {
        total,
        pinned,
        archived,
        totalWords,
        averageWords
      };
    } catch (error) {
      console.error('Error getting note stats:', error);
      throw new Error('Failed to get note stats');
    }
  }

  // Duplicate note
  async duplicateNote(id: string, newTitle?: string): Promise<Note> {
    try {
      const originalNote = await db.notes.get(id);
      if (!originalNote) {
        throw new Error('Note not found');
      }

      const duplicatedNote: Note = {
        ...originalNote,
        id: uuidv4(),
        title: newTitle || `${originalNote.title} (Copy)`,
        isPinned: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.notes.add(duplicatedNote);

      // Update search index
      await db.updateSearchIndex(
        'note',
        duplicatedNote.id,
        duplicatedNote.title,
        duplicatedNote.content,
        duplicatedNote.tags
      );

      // Log activity
      await db.logActivity(
        'current-user',
        'duplicate',
        'note',
        duplicatedNote.id,
        { originalId: id, title: duplicatedNote.title }
      );

      return duplicatedNote;
    } catch (error) {
      console.error('Error duplicating note:', error);
      throw new Error('Failed to duplicate note');
    }
  }

  // Bulk operations
  async bulkUpdateNotes(noteIds: string[], updates: Partial<Omit<Note, 'id' | 'createdAt'>>): Promise<void> {
    try {
      await db.transaction('rw', db.notes, async () => {
        for (const noteId of noteIds) {
          await db.notes.update(noteId, { ...updates, updatedAt: new Date() });
        }
      });

      // Log activity
      await db.logActivity(
        'current-user',
        'bulk_update',
        'note',
        noteIds.join(','),
        { count: noteIds.length, changes: updates }
      );
    } catch (error) {
      console.error('Error bulk updating notes:', error);
      throw new Error('Failed to bulk update notes');
    }
  }

  async bulkDeleteNotes(noteIds: string[]): Promise<void> {
    try {
      await db.transaction('rw', [db.notes, db.searchIndex, db.comments], async () => {
        // Delete notes
        await db.notes.where('id').anyOf(noteIds).delete();
        
        // Delete from search index
        await db.searchIndex.where('entityId').anyOf(noteIds).delete();
        
        // Delete related comments
        await db.comments.where('entityId').anyOf(noteIds).delete();
      });

      // Log activity
      await db.logActivity(
        'current-user',
        'bulk_delete',
        'note',
        noteIds.join(','),
        { count: noteIds.length }
      );
    } catch (error) {
      console.error('Error bulk deleting notes:', error);
      throw new Error('Failed to bulk delete notes');
    }
  }

  // Helper method to calculate word count
  private calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    
    // Remove HTML tags and extra whitespace, then count words
    const plainText = content.replace(/<[^>]*>/g, '').trim();
    if (plainText.length === 0) {
      return 0;
    }
    
    return plainText.split(/\s+/).length;
  }
}

// Export singleton instance
export const noteService = new NoteService();
