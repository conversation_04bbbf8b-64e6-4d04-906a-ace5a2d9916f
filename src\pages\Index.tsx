
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import Sidebar from '@/components/Layout/Sidebar';
import Dashboard from '@/components/Dashboard/Dashboard';
import TasksList from '@/components/Tasks/TasksList';
import ProjectsGrid from '@/components/Projects/ProjectsGrid';
import NotesGrid from '@/components/Notes/NotesGrid';
import AnalyticsDashboard from '@/components/Analytics/AnalyticsDashboard';
import Settings from '@/components/Settings/Settings';
import SmartSearch from '@/components/Search/SmartSearch';
import AIAssistant from '@/components/AI/AIAssistant';
import FocusMode from '@/components/Focus/FocusMode';
import { cn } from '@/lib/utils';

const Index = () => {
  const { t, isRTL } = useLanguage();
  const location = useLocation();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showFocusMode, setShowFocusMode] = useState(false);

  // Handle navigation from detail pages
  useEffect(() => {
    if (location.state?.activeSection) {
      setActiveSection(location.state.activeSection);
    }
  }, [location.state]);

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <Dashboard />;
      case 'tasks':
        return <TasksList />;
      case 'projects':
        return <ProjectsGrid />;
      case 'notes':
        return <NotesGrid />;
      case 'analytics':
        return <AnalyticsDashboard />;
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className={cn(
      "flex min-h-screen bg-background font-arabic transition-colors duration-200",
      isRTL ? "rtl" : "ltr"
    )} dir={isRTL ? "rtl" : "ltr"}>
      <Sidebar 
        activeSection={activeSection} 
        onSectionChange={setActiveSection} 
      />
      <main className="flex-1 overflow-auto">
        {/* Search Bar */}
        <div className="sticky top-0 z-30 bg-background/80 backdrop-blur-lg border-b border-border p-4 transition-colors duration-200">
          <div className={cn(
            "max-w-7xl mx-auto flex items-center justify-between gap-4",
            isRTL && "flex-row-reverse"
          )}>
            <SmartSearch />
            <button
              onClick={() => setShowFocusMode(true)}
              className="px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200 font-medium whitespace-nowrap"
            >
              {t('focus.title')}
            </button>
          </div>
        </div>
        
        {renderActiveSection()}
      </main>

      {/* AI Assistant */}
      <AIAssistant />

      {/* Focus Mode */}
      {showFocusMode && (
        <FocusMode onClose={() => setShowFocusMode(false)} />
      )}
    </div>
  );
};

export default Index;
